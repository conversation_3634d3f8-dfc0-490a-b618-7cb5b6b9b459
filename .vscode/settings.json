{"files.associations": {"__hash_table": "cpp", "__node_handle": "cpp", "__threading_support": "cpp", "__verbose_abort": "cpp", "cctype": "cpp", "cmath": "cpp", "cstddef": "cpp", "cstdint": "cpp", "cstdlib": "cpp", "cstring": "cpp", "ctime": "cpp", "cwchar": "cpp", "cwctype": "cpp", "execution": "cpp", "initializer_list": "cpp", "iosfwd": "cpp", "limits": "cpp", "new": "cpp", "optional": "cpp", "ratio": "cpp", "stdexcept": "cpp", "tuple": "cpp", "typeinfo": "cpp", "unordered_map": "cpp", "variant": "cpp", "charconv": "cpp", "memory": "cpp", "forward_list": "cpp", "locale": "cpp", "mutex": "cpp", "sstream": "cpp", "string": "cpp", "__assertion_handler": "cpp", "iostream": "cpp", "queue": "cpp", "stack": "cpp", "map": "cpp", "__tree": "cpp", "set": "cpp", "string_view": "cpp", "ostream": "cpp", "algorithm": "cpp", "fstream": "cpp", "vector": "cpp", "__locale": "cpp"}}