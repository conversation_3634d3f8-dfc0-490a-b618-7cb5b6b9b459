# This CmakeLists.txt only for Clion IDE local development, It not working for build project
cmake_minimum_required(VERSION 3.10)
project(EzorsiaV2 CXX)

set(CMAKE_SYSTEM_NAME Windows)
set(CMAKE_FIND_ROOT_PATH /usr/local/Cellar/mingw-w64/12.0.0_1/toolchain-x86_64)
set(CMAKE_C_COMPILER ${CMAKE_FIND_ROOT_PATH}/bin/x86_64-w64-mingw32-gcc)
set(CMAKE_CXX_COMPILER ${CMAKE_FIND_ROOT_PATH}/bin/x86_64-w64-mingw32-g++)
set(CMAKE_RC_COMPILER ${CMAKE_FIND_ROOT_PATH}/bin/x86_64-w64-mingw32-windres)

add_library(EzorsiaV2 SHARED
        ezorsia/Client.cpp
        ezorsia/dinput8.cpp
        ezorsia/MainMain.cpp
        ezorsia/ZAllocEx.cpp
        ezorsia/dllmain.cpp
        ezorsia/Memory.cpp
        ezorsia/stdafx.cpp
        ezorsia/linkfix.cpp
)

target_include_directories(EzorsiaV2 PRIVATE
        ezorsia
        ezorsia/MapleClientCollectionTypes
)
