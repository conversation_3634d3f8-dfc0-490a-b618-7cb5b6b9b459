# clang-format configuration for MagicMS project
# Based on Google style with customizations for Windows DLL development

BasedOnStyle: Google
Language: Cpp

# Indentation
IndentWidth: 4
TabWidth: 4
UseTab: Never
IndentCaseLabels: true
IndentPPDirectives: BeforeHash

# Line length
ColumnLimit: 120

# Braces
BreakBeforeBraces: Allman
BreakConstructorInitializers: BeforeComma
BreakInheritanceList: BeforeComma

# Functions
AllowShortFunctionsOnASingleLine: None
AlwaysBreakAfterDefinitionReturnType: None
AlwaysBreakAfterReturnType: None

# Control flow
AllowShortIfStatementsOnASingleLine: false
AllowShortLoopsOnASingleLine: false
AllowShortCaseLabelsOnASingleLine: false

# Spacing
SpaceAfterCStyleCast: true
SpaceAfterLogicalNot: false
SpaceAfterTemplateKeyword: true
SpaceBeforeAssignmentOperators: true
SpaceBeforeCpp11BracedList: false
SpaceBeforeParens: ControlStatements
SpaceInEmptyParentheses: false
SpacesBeforeTrailingComments: 2
SpacesInAngles: false
SpacesInCStyleCastParentheses: false
SpacesInParentheses: false
SpacesInSquareBrackets: false

# Alignment
AlignAfterOpenBracket: Align
AlignConsecutiveAssignments: false
AlignConsecutiveDeclarations: false
AlignEscapedNewlines: Right
AlignOperands: true
AlignTrailingComments: true

# Breaking
AllowAllParametersOfDeclarationOnNextLine: true
AllowShortBlocksOnASingleLine: false
BinPackArguments: true
BinPackParameters: true
BreakBeforeBinaryOperators: None
BreakBeforeTernaryOperators: true
BreakStringLiterals: true

# Sorting
SortIncludes: true
SortUsingDeclarations: true

# Pointers and references
DerivePointerAlignment: false
PointerAlignment: Left

# Namespaces
CompactNamespaces: false
FixNamespaceComments: true
NamespaceIndentation: None

# Comments
ReflowComments: true

# Other
KeepEmptyLinesAtTheStartOfBlocks: false
MaxEmptyLinesToKeep: 2
