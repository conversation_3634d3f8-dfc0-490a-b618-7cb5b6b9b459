# MapleEzorsia v2 变量名批量替换清单

## 说明
这个文件包含了所有需要进行批量替换的变量名映射。请使用你的编辑器的"查找和替换"功能，按照下面的映射表进行全项目替换。

## 替换映射表

### 主要变量替换 (在所有文件中)
```
m_nGameHeight -> gameHeight
m_nGameWidth -> gameWidth
m_nIGCipherHash -> igCipherHash
WindowedMode -> windowedMode
RemoveLogos -> removeLogos
MsgAmount -> msgAmount
setDamageCap -> damageCap
ServerIP_AddressFromINI -> serverIPAddress
InOutLinkSupport -> inOutLinkSupport
```

### 函数名替换 (在所有文件中)
```
UpdateGameStartup -> updateGameStartup
UpdateResolution -> updateResolution
UpdateLogin -> updateLogin
EnableNewIGCipher -> enableNewIGCipher
JumpCap -> jumpCap
```

### 类型替换 (仅在头文件中)
```
DWORD jumpCap -> uint32_t jumpCap
```

## 需要检查的文件列表
以下文件包含需要替换的变量引用：

### 核心文件
- `ezorsia/Client.h` ✅ (已完成大部分)
- `ezorsia/Client.cpp` ⚠️ (还有约200+个m_n引用需要替换)
- `ezorsia/MainMain.cpp` ✅ (已完成)
- `ezorsia/dllmain.cpp` ✅ (已完成)

### 辅助文件
- `ezorsia/codecaves.h` ⚠️ (可能还有jumpCap引用)
- `ezorsia/ReplacementFuncs.h` ✅ (已完成)
- `ezorsia/AutoTypes.h` (可能包含相关引用)

### 文档文件 (可选更新)
- `.refactor/architectural-analysis.md`
- `.refactor/code-quality-analysis.md`
- `.refactor/testing-gaps-analysis.md`

## 批量替换建议步骤

### 步骤1: 使用IDE全局查找替换
1. 打开你的IDE (如VSCode, Visual Studio等)
2. 使用 Ctrl+Shift+H (或对应的全局替换快捷键)
3. 按照上面的映射表逐一替换

### 步骤2: 重点关注的文件
**ezorsia/Client.cpp** 是最需要关注的文件，包含大量需要替换的引用：
- 约200+个 `m_nGameHeight` 需要替换为 `gameHeight`
- 约200+个 `m_nGameWidth` 需要替换为 `gameWidth`

### 步骤3: 验证替换
替换完成后，请检查：
1. 确保没有遗漏的旧变量名
2. 确保没有误替换 (如注释中的说明文字)
3. 确保语法正确

## 特殊注意事项

### 不要替换的情况
1. **注释中的说明文字**: 如果注释中提到旧变量名作为说明，可以保留
2. **字符串字面量**: 配置文件中的键名不需要替换
3. **宏定义**: 某些宏定义可能需要保持原样

### 需要手动检查的地方
1. **函数调用**: 确保所有函数调用都使用新的函数名
2. **变量声明**: 确保所有变量声明都使用新的变量名
3. **类成员访问**: 确保 `Client::variableName` 格式正确

## 替换后的验证清单
- [ ] 所有 `m_nGameHeight` 已替换为 `gameHeight`
- [ ] 所有 `m_nGameWidth` 已替换为 `gameWidth`
- [ ] 所有 `WindowedMode` 已替换为 `windowedMode`
- [ ] 所有 `RemoveLogos` 已替换为 `removeLogos`
- [ ] 所有函数名已更新为camelCase格式
- [ ] 没有语法错误
- [ ] 没有误替换注释或字符串

## 完成后通知
替换完成后，请告知我，我将进行：
1. 代码检查和验证
2. 编译测试
3. 更新重构进度文档
4. 准备下一阶段的重构任务
