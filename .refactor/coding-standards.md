# 编码标准和约定

## 命名约定

### 类名
- 使用 PascalCase（首字母大写）
- 例如：`ClientConfig`, `MemoryManager`, `LoggerService`

### 函数名
- 公共函数使用 PascalCase
- 私有函数使用 camelCase
- 例如：`GetGameWidth()`, `setWindowMode()`

### 变量名
- 成员变量使用 m_ 前缀 + camelCase
- 静态变量使用 s_ 前缀 + camelCase
- 常量使用 k 前缀 + PascalCase 或全大写
- 例如：`m_gameWidth`, `s_instance`, `kDefaultWidth`, `MAX_BUFFER_SIZE`

### 常量和宏
- 使用全大写 + 下划线
- 例如：`MAX_GAME_WIDTH`, `DEFAULT_SERVER_PORT`

## 代码风格

### 头文件
```cpp
#pragma once

#include <system_headers>
#include "project_headers.h"

// 前向声明
class SomeClass;

namespace ProjectNamespace {
    // 类定义
}
```

### 类定义
```cpp
class ExampleClass {
public:
    // 构造函数和析构函数
    ExampleClass();
    ~ExampleClass();
    
    // 公共方法
    void PublicMethod();
    
private:
    // 私有方法
    void privateMethod();
    
    // 成员变量
    int m_memberVariable;
    static int s_staticVariable;
};
```

### 函数定义
```cpp
ReturnType FunctionName(ParamType param1, ParamType param2) {
    // 函数体
    if (condition) {
        // 缩进使用 4 个空格
        DoSomething();
    }
    
    return value;
}
```

## 注释约定

### 文件头注释
```cpp
/**
 * @file FileName.h
 * @brief 文件的简要描述
 * <AUTHOR>
 * @date 创建日期
 */
```

### 类注释
```cpp
/**
 * @brief 类的简要描述
 * 
 * 详细描述类的功能和用途
 */
class ExampleClass {
    // ...
};
```

### 函数注释
```cpp
/**
 * @brief 函数的简要描述
 * @param param1 参数1的描述
 * @param param2 参数2的描述
 * @return 返回值的描述
 * @throws ExceptionType 可能抛出的异常
 */
ReturnType FunctionName(ParamType param1, ParamType param2);
```

## 错误处理

### 返回值检查
```cpp
bool result = SomeFunction();
if (!result) {
    // 处理错误
    LogError("Function failed");
    return false;
}
```

### 异常处理
```cpp
try {
    RiskyOperation();
} catch (const std::exception& e) {
    LogError("Operation failed: {}", e.what());
    // 处理或重新抛出
}
```

## 内存管理

### 优先使用智能指针
```cpp
std::unique_ptr<Resource> resource = std::make_unique<Resource>();
std::shared_ptr<SharedResource> shared = std::make_shared<SharedResource>();
```

### RAII 模式
```cpp
class ResourceManager {
public:
    ResourceManager() { 
        resource_ = AcquireResource(); 
    }
    
    ~ResourceManager() { 
        ReleaseResource(resource_); 
    }
    
private:
    ResourceHandle resource_;
};
```

## 常量定义

### 地址常量
```cpp
namespace GameAddresses {
    constexpr DWORD kLoginScreenBase = 0x009F7159;
    constexpr DWORD kResolutionWidth = 0x008B4568;
    constexpr DWORD kResolutionHeight = 0x008B456C;
}
```

### 配置常量
```cpp
namespace GameConfig {
    constexpr int kDefaultWidth = 1280;
    constexpr int kDefaultHeight = 720;
    constexpr int kMaxMsgAmount = 50;
}
```

## 文件组织

### 头文件依赖
- 最小化头文件包含
- 优先使用前向声明
- 系统头文件在前，项目头文件在后

### 模块划分
```
/core/           - 核心功能
/ui/             - UI 相关
/memory/         - 内存管理
/network/        - 网络功能
/config/         - 配置管理
/utils/          - 工具函数
```

## 版本控制

### 提交信息格式
```
type(scope): subject

body

footer
```

例如：
```
feat(client): add new resolution support

- Added support for 1920x1080 resolution
- Updated UI scaling calculations
- Fixed aspect ratio issues

Closes #123
```

## 测试要求

### 单元测试
- 每个公共函数都应有对应测试
- 测试文件命名：`ClassNameTest.cpp`

### 集成测试
- 关键功能路径的端到端测试
- 内存泄漏检查
- 性能基准测试
