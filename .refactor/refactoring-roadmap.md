# MapleEzorsia v2 重构路线图

## 重构原则
基于用户偏好的系统性重构方法：
1. **低风险优先**: 从简单、安全的改动开始
2. **渐进式改进**: 每次修改范围小，易于验证
3. **文档同步**: 每个节点都更新文档和进度
4. **功能验证**: 确保每次修改后功能完整

## 阶段划分

### 🟢 阶段1: 低风险重构 (1-2周)
**目标**: 改善代码可读性和基础质量，不改变核心逻辑

#### 1.1 代码风格统一 (2-3天)
- [ ] 统一命名约定 (匈牙利记号法 → 现代C++风格)
- [ ] 格式化代码 (缩进、空格、换行)
- [ ] 移除无用注释和代码
- [ ] 统一头文件包含顺序

#### 1.2 常量提取和命名 (2-3天)
- [ ] 提取魔法数字为命名常量
- [ ] 创建GameConstants命名空间
- [ ] 重命名不清晰的变量名
- [ ] 添加基础文档注释

#### 1.3 简单函数重构 (2-3天)
- [ ] 拆分过长的函数 (保持原有逻辑)
- [ ] 提取重复代码为工具函数
- [ ] 改善函数参数命名
- [ ] 添加函数文档

### 🟡 阶段2: 中风险重构 (2-3周)
**目标**: 改善代码结构和组织，减少全局依赖

#### 2.1 配置管理重构 (1周)
- [ ] 创建ConfigManager类
- [ ] 封装INIReader使用
- [ ] 添加配置验证
- [ ] 统一配置访问接口

#### 2.2 错误处理标准化 (1周)
- [ ] 创建统一的错误处理机制
- [ ] 替换直接ExitProcess调用
- [ ] 添加错误日志记录
- [ ] 改善异常安全性

#### 2.3 Client类重构 (1周)
- [ ] 拆分Client类职责
- [ ] 创建GameConfig、NetworkConfig等专门类
- [ ] 减少静态成员变量
- [ ] 改善类接口设计

### 🔴 阶段3: 高风险重构 (3-4周)
**目标**: 架构级改进，需要大量测试验证

#### 3.1 内存管理抽象化 (1-2周)
- [ ] 创建IMemoryPatcher接口
- [ ] 实现AddressManager
- [ ] 封装直接内存操作
- [ ] 添加地址验证机制

#### 3.2 模块化重构 (1-2周)
- [ ] 重新组织文件结构
- [ ] 创建清晰的模块边界
- [ ] 解决循环依赖问题
- [ ] 建立分层架构

#### 3.3 初始化流程重构 (1周)
- [ ] 重构DllMain逻辑
- [ ] 创建Application类
- [ ] 实现初始化器链模式
- [ ] 改善生命周期管理

## 详细任务分解

### 阶段1详细任务

#### 任务1.1: 命名约定统一
**预估时间**: 1天
**风险等级**: 🟢 低
**验证方式**: 编译通过，功能测试

**具体步骤**:
1. 统一变量命名 (去除匈牙利记号)
   ```cpp
   // 前: DWORD dwOriginAddress
   // 后: uint32_t originAddress
   ```
2. 统一函数命名 (PascalCase → camelCase)
   ```cpp
   // 前: UpdateGameStartup()
   // 后: updateGameStartup()
   ```
3. 统一常量命名 (全大写+下划线)
   ```cpp
   // 前: const DWORD dwDInput8DLLInject
   // 后: const uint32_t DINPUT8_DLL_INJECT_ADDRESS
   ```

#### 任务1.2: 魔法数字提取
**预估时间**: 2天
**风险等级**: 🟢 低
**验证方式**: 功能对比测试

**具体步骤**:
1. 创建GameConstants.h
   ```cpp
   namespace GameConstants {
       namespace Addresses {
           constexpr uint32_t DINPUT8_DLL_INJECT = 0x00796357;
           constexpr uint32_t MOVEMENT_FLUSH_INTERVAL = 0x0068A83F;
       }
       namespace Values {
           constexpr uint8_t LOGIN_PATCH_VALUE = 0x08;
           constexpr uint8_t SECURITY_BYPASS_VALUE = 0xA0;
       }
   }
   ```
2. 替换所有硬编码数字
3. 添加注释说明每个常量的用途

#### 任务1.3: 函数拆分
**预估时间**: 2天
**风险等级**: 🟢 低
**验证方式**: 单元测试 + 功能测试

**具体步骤**:
1. 拆分Client::UpdateGameStartup() (1101行 → 多个小函数)
   ```cpp
   void Client::updateGameStartup() {
       applySecurityPatches();
       applyUIPatches();
       applyNetworkPatches();
       applyLanguagePatches();
   }
   ```
2. 提取重复的内存操作为工具函数
3. 每个函数保持单一职责

### 阶段2详细任务

#### 任务2.1: 配置管理重构
**预估时间**: 5天
**风险等级**: 🟡 中
**验证方式**: 配置测试 + 集成测试

**具体步骤**:
1. 创建ConfigManager类 (1天)
   ```cpp
   class ConfigManager {
   public:
       static ConfigManager& getInstance();
       GameConfig getGameConfig() const;
       NetworkConfig getNetworkConfig() const;
       bool validateConfig() const;
   };
   ```
2. 创建强类型配置结构 (1天)
3. 添加配置验证逻辑 (1天)
4. 重构所有配置访问点 (2天)

#### 任务2.2: Client类拆分
**预估时间**: 5天
**风险等级**: 🟡 中
**验证方式**: 单元测试 + 功能测试

**具体步骤**:
1. 创建GameConfig类 (1天)
2. 创建NetworkConfig类 (1天)
3. 创建ClientPatcher类 (2天)
4. 重构Client类，移除静态变量 (1天)

### 阶段3详细任务

#### 任务3.1: 内存管理抽象化
**预估时间**: 7天
**风险等级**: 🔴 高
**验证方式**: 单元测试 + 集成测试 + 手动验证

**具体步骤**:
1. 设计IMemoryPatcher接口 (1天)
2. 实现MemoryPatcher类 (2天)
3. 创建AddressManager (2天)
4. 重构所有内存操作调用 (2天)

## 进度跟踪

### 完成标准
每个任务完成需要满足：
1. ✅ 代码编译通过
2. ✅ 功能测试通过
3. ✅ 代码审查通过
4. ✅ 文档更新完成

### 里程碑检查点
- **第1周末**: 阶段1完成50%
- **第2周末**: 阶段1完成，阶段2开始
- **第4周末**: 阶段2完成
- **第7周末**: 阶段3完成50%
- **第8周末**: 全部重构完成

### 风险缓解
1. **每日备份**: 每天提交代码到版本控制
2. **功能验证**: 每个任务完成后立即测试
3. **回滚准备**: 保持可以快速回滚的能力
4. **文档同步**: 实时更新重构文档

## 成功指标

### 代码质量指标
- 代码行数减少15-20%
- 函数平均长度 < 50行
- 类的静态成员 < 5个
- 魔法数字 < 10个

### 可维护性指标
- 新功能添加时间减少50%
- Bug修复时间减少30%
- 代码审查时间减少40%

### 稳定性指标
- 编译警告 = 0
- 内存泄漏 = 0
- 崩溃率 < 0.1%
