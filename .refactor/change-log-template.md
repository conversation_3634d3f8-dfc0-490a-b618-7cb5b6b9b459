# 代码变更日志模板

## 变更记录格式

### 变更类型
- 🔧 **REFACTOR**: 重构代码，不改变功能
- ✨ **FEATURE**: 新增功能
- 🐛 **BUGFIX**: 修复错误
- 📝 **DOCS**: 文档更新
- 🎨 **STYLE**: 代码格式化，不影响功能
- ⚡ **PERF**: 性能优化
- 🧪 **TEST**: 添加或修改测试
- 🔒 **SECURITY**: 安全相关修改

### 变更记录模板

```markdown
## [日期] - [变更类型] [简短描述]

### 变更详情
- **文件**: 受影响的文件列表
- **函数/类**: 具体修改的函数或类
- **变更原因**: 为什么进行这个变更
- **变更内容**: 具体做了什么修改

### 影响评估
- **风险等级**: 🟢低 / 🟡中 / 🔴高
- **影响范围**: 描述可能受影响的功能
- **向后兼容**: 是否保持向后兼容

### 验证方式
- [ ] 编译通过
- [ ] 单元测试通过
- [ ] 功能测试通过
- [ ] 性能测试通过 (如适用)

### 回滚计划
如果出现问题，如何快速回滚此变更

---
```

## 示例变更记录

### [2025-06-27] - 🔧 REFACTOR 统一变量命名约定

#### 变更详情
- **文件**: 
  - `ezorsia/Client.h`
  - `ezorsia/Client.cpp`
  - `ezorsia/MainMain.h`
  - `ezorsia/MainMain.cpp`
- **函数/类**: Client类, MainMain类的所有成员变量
- **变更原因**: 统一代码风格，提高可读性，符合现代C++命名约定
- **变更内容**: 
  - 将匈牙利命名法改为现代C++风格
  - `m_nGameWidth` → `gameWidth`
  - `dwOriginAddress` → `originAddress`
  - `ucValue` → `value`

#### 影响评估
- **风险等级**: 🟢低
- **影响范围**: 仅影响变量名，不影响功能逻辑
- **向后兼容**: 是，不影响外部接口

#### 验证方式
- [x] 编译通过
- [x] 单元测试通过
- [x] 功能测试通过
- [ ] 性能测试通过 (不适用)

#### 回滚计划
使用git revert命令回滚到变更前的commit

---

### [2025-06-27] - 🔧 REFACTOR 提取魔法数字为命名常量

#### 变更详情
- **文件**: 
  - `ezorsia/GameConstants.h` (新建)
  - `ezorsia/Client.cpp`
  - `ezorsia/AddyLocations.h`
- **函数/类**: 新增GameConstants命名空间
- **变更原因**: 消除魔法数字，提高代码可维护性
- **变更内容**: 
  - 创建GameConstants命名空间
  - 将硬编码数字提取为命名常量
  - 添加注释说明每个常量的用途

#### 影响评估
- **风险等级**: 🟢低
- **影响范围**: 不影响功能，仅改善代码可读性
- **向后兼容**: 是

#### 验证方式
- [x] 编译通过
- [x] 单元测试通过
- [x] 功能测试通过
- [ ] 性能测试通过 (不适用)

#### 回滚计划
删除GameConstants.h文件，恢复原有的硬编码数字

---

## 变更审查清单

### 代码质量检查
- [ ] 代码符合项目编码规范
- [ ] 没有引入新的警告或错误
- [ ] 变量和函数命名清晰明确
- [ ] 注释充分且准确
- [ ] 没有重复代码

### 功能完整性检查
- [ ] 所有现有功能正常工作
- [ ] 没有破坏现有的API接口
- [ ] 错误处理机制完整
- [ ] 边界条件处理正确

### 性能影响检查
- [ ] 没有引入性能回归
- [ ] 内存使用合理
- [ ] 没有引入内存泄漏
- [ ] 启动时间没有明显增加

### 安全性检查
- [ ] 没有引入安全漏洞
- [ ] 输入验证充分
- [ ] 内存操作安全
- [ ] 没有缓冲区溢出风险

### 测试覆盖检查
- [ ] 新代码有相应的测试
- [ ] 现有测试仍然通过
- [ ] 测试覆盖率没有下降
- [ ] 集成测试通过

### 文档更新检查
- [ ] API文档已更新
- [ ] 用户文档已更新 (如适用)
- [ ] 变更日志已更新
- [ ] 代码注释已更新

## 变更批准流程

### 低风险变更 (🟢)
- 自动化测试通过
- 代码审查通过
- 可以直接合并

### 中风险变更 (🟡)
- 自动化测试通过
- 代码审查通过
- 手动功能测试通过
- 需要额外审查

### 高风险变更 (🔴)
- 自动化测试通过
- 代码审查通过
- 完整的回归测试通过
- 多人审查批准
- 制定详细的回滚计划
