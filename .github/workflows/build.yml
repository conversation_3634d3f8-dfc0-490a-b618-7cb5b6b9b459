name: Build Ezorsia V2 Project

on:
  push:
    branches:
      - main
      - dev
      - dev-refactor
  pull_request:
    branches:
      - main

jobs:
  build:
    runs-on: windows-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v2

      - name: Set up MSBuild
        uses: microsoft/setup-msbuild@v1.1

      - name: Build with MSBuild
        shell: pwsh
        run: |
          $envFile = ".env"
          $clDefinitions = @()
          
          $clDefinitions += "/utf-8"
          
          if (Test-Path $envFile) {
              Get-Content $envFile | ForEach-Object {
                  $line = $_.Trim()
                  if ($line -and !$line.StartsWith("#")) {
                      $parts = $line -split '=', 2
                      if ($parts.Count -eq 2) {
                          $name = $parts[0].Trim()
                          $value = $parts[1].Trim()
                          echo "$name=$value" >> env_vars.txt
                          if ($value -ne "") {
                              $clDefinitions += "/D$name=$value"
                          } else {
                              $clDefinitions += "/D$name"
                          }
                      }
                  }
              }
          }
          
          $env:CL = $clDefinitions -join ' '
          
          Write-Output "CL environment variable: $env:CL"
          
          msbuild "Ezorsia V2.sln" /p:Configuration=Release /p:Platform=x86 /p:CharacterSet=Unicode

      - name: Create ZIP package
        run: |
          mkdir output
          powershell -command "Copy-Item -Path 'out\Release\*' -Destination 'output\' -Recurse"
          powershell -command "Compress-Archive -Path 'output\*' -DestinationPath 'EzorsiaV2Release.zip'"

      - name: Generate Release Tag and Description
        id: generate_tag_and_desc
        run: |
          $branch_name = $env:GITHUB_REF -replace 'refs/heads/', ''
          $timestamp = (Get-Date).ToUniversalTime().ToString("ddHHmmss")
          $commit_id = git rev-parse --short=6 HEAD
          
          $new_tag = "$branch_name-$commit_id-$timestamp"
          Write-Host "Generated tag: $new_tag"
          echo "::set-output name=tag::$new_tag"
  
          $desc = "Build Environment:`n`n"
          $desc += Get-Content env_vars.txt -Raw
          $desc | Out-File -FilePath release_description.txt -Encoding utf8
          echo "::set-output name=description::$desc"

      - name: Upload Release Asset
        uses: softprops/action-gh-release@v1
        with:
          tag_name: ${{ steps.generate_tag_and_desc.outputs.tag }}
          body_path: release_description.txt
          files: EzorsiaV2Release.zip
          prerelease: ${{ startsWith(github.ref, 'refs/heads/dev') }}
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
