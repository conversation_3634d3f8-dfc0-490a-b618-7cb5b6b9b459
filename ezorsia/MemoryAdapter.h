#pragma once
#include "MemoryManager.h"

namespace EzorsiaMemory {

// 适配器类，提供与原Memory类兼容的接口
class MemoryAdapter {
public:
    // 静态方法，保持与原Memory类的兼容性
    static bool SetHook(bool attach, void** ptrTarget, void* ptrDetour);
    static void FillBytes(DWORD dwOriginAddress, unsigned char ucValue, int nCount);
    static void WriteString(DWORD dwOriginAddress, const char* sContent);
    static void WriteByte(DWORD dwOriginAddress, unsigned char ucValue);
    static void WriteShort(DWORD dwOriginAddress, unsigned short usValue);
    static void WriteInt(DWORD dwOriginAddress, unsigned int dwValue);
    static void WriteDouble(DWORD dwOriginAddress, double dwValue);
    static void CodeCave(void* ptrCodeCave, DWORD dwOriginAddress, int nNOPCount);
    static void WriteByteArray(DWORD dwOriginAddress, unsigned char* ucValue, const int ucValueSize);
    
    // 新增的安全版本，返回Result类型
    static Result<void> SafeSetHook(bool attach, void** ptrTarget, void* ptrDetour);
    static Result<void> SafeFillBytes(DWORD dwOriginAddress, unsigned char ucValue, int nCount);
    static Result<void> SafeWriteString(DWORD dwOriginAddress, const char* sContent);
    static Result<void> SafeWriteByte(DWORD dwOriginAddress, unsigned char ucValue);
    static Result<void> SafeWriteShort(DWORD dwOriginAddress, unsigned short usValue);
    static Result<void> SafeWriteInt(DWORD dwOriginAddress, unsigned int dwValue);
    static Result<void> SafeWriteDouble(DWORD dwOriginAddress, double dwValue);
    static Result<void> SafeCodeCave(void* ptrCodeCave, DWORD dwOriginAddress, int nNOPCount);
    static Result<void> SafeWriteByteArray(DWORD dwOriginAddress, unsigned char* ucValue, const int ucValueSize);
    
    // 地址管理相关方法
    static Result<void> RegisterAddress(const std::string& name, DWORD address, size_t size = 1);
    static Result<DWORD> GetRegisteredAddress(const std::string& name);
    static Result<void> ValidateAddress(const std::string& name);
    
    // 配置方法
    static void SetUseVirtualProtect(bool enable);
    static bool GetUseVirtualProtect();
    
    // 错误处理
    static void SetErrorHandler(std::function<void(const std::string&)> handler);
    static void LogError(const std::string& operation, const std::string& error);
    
private:
    static bool useVirtualProtect_;
    static std::function<void(const std::string&)> errorHandler_;
    
    // 内部辅助方法
    static void handleError(const std::string& operation, const Result<void>& result);
    template<typename T>
    static T handleErrorWithReturn(const std::string& operation, const Result<T>& result, T defaultValue);
};

} // namespace EzorsiaMemory

// 全局宏定义，用于逐步迁移
#define MEMORY_SAFE_MODE 1

#if MEMORY_SAFE_MODE
    // 在安全模式下，使用新的内存管理器
    #define Memory_SetHook(attach, target, detour) \
        EzorsiaMemory::MemoryAdapter::SetHook(attach, target, detour)
    #define Memory_FillBytes(addr, value, count) \
        EzorsiaMemory::MemoryAdapter::FillBytes(addr, value, count)
    #define Memory_WriteString(addr, content) \
        EzorsiaMemory::MemoryAdapter::WriteString(addr, content)
    #define Memory_WriteByte(addr, value) \
        EzorsiaMemory::MemoryAdapter::WriteByte(addr, value)
    #define Memory_WriteShort(addr, value) \
        EzorsiaMemory::MemoryAdapter::WriteShort(addr, value)
    #define Memory_WriteInt(addr, value) \
        EzorsiaMemory::MemoryAdapter::WriteInt(addr, value)
    #define Memory_WriteDouble(addr, value) \
        EzorsiaMemory::MemoryAdapter::WriteDouble(addr, value)
    #define Memory_CodeCave(cave, addr, nops) \
        EzorsiaMemory::MemoryAdapter::CodeCave(cave, addr, nops)
    #define Memory_WriteByteArray(addr, array, size) \
        EzorsiaMemory::MemoryAdapter::WriteByteArray(addr, array, size)
#else
    // 在兼容模式下，使用原始Memory类
    #define Memory_SetHook(attach, target, detour) \
        Memory::SetHook(attach, target, detour)
    #define Memory_FillBytes(addr, value, count) \
        Memory::FillBytes(addr, value, count)
    #define Memory_WriteString(addr, content) \
        Memory::WriteString(addr, content)
    #define Memory_WriteByte(addr, value) \
        Memory::WriteByte(addr, value)
    #define Memory_WriteShort(addr, value) \
        Memory::WriteShort(addr, value)
    #define Memory_WriteInt(addr, value) \
        Memory::WriteInt(addr, value)
    #define Memory_WriteDouble(addr, value) \
        Memory::WriteDouble(addr, value)
    #define Memory_CodeCave(cave, addr, nops) \
        Memory::CodeCave(cave, addr, nops)
    #define Memory_WriteByteArray(addr, array, size) \
        Memory::WriteByteArray(addr, array, size)
#endif
