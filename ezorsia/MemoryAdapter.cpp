#include "stdafx.h"
#include "MemoryAdapter.h"
#include <iostream>

namespace EzorsiaMemory {

// 静态成员初始化
bool MemoryAdapter::useVirtualProtect_ = true;
std::function<void(const std::string&)> MemoryAdapter::errorHandler_ = nullptr;

// 兼容性方法实现
bool MemoryAdapter::SetHook(bool attach, void** ptrTarget, void* ptrDetour) {
    auto result = SafeSetHook(attach, ptrTarget, ptrDetour);
    if (!result) {
        handleError("SetHook", result);
        return false;
    }
    return true;
}

void MemoryAdapter::FillBytes(DWORD dwOriginAddress, unsigned char ucValue, int nCount) {
    auto result = SafeFillBytes(dwOriginAddress, ucValue, nCount);
    if (!result) {
        handleError("FillBytes", result);
    }
}

void MemoryAdapter::WriteString(DWORD dwOriginAddress, const char* sContent) {
    auto result = SafeWriteString(dwOriginAddress, sContent);
    if (!result) {
        handleError("WriteString", result);
    }
}

void MemoryAdapter::WriteByte(DWORD dwOriginAddress, unsigned char ucValue) {
    auto result = SafeWriteByte(dwOriginAddress, ucValue);
    if (!result) {
        handleError("WriteByte", result);
    }
}

void MemoryAdapter::WriteShort(DWORD dwOriginAddress, unsigned short usValue) {
    auto result = SafeWriteShort(dwOriginAddress, usValue);
    if (!result) {
        handleError("WriteShort", result);
    }
}

void MemoryAdapter::WriteInt(DWORD dwOriginAddress, unsigned int dwValue) {
    auto result = SafeWriteInt(dwOriginAddress, dwValue);
    if (!result) {
        handleError("WriteInt", result);
    }
}

void MemoryAdapter::WriteDouble(DWORD dwOriginAddress, double dwValue) {
    auto result = SafeWriteDouble(dwOriginAddress, dwValue);
    if (!result) {
        handleError("WriteDouble", result);
    }
}

void MemoryAdapter::CodeCave(void* ptrCodeCave, DWORD dwOriginAddress, int nNOPCount) {
    auto result = SafeCodeCave(ptrCodeCave, dwOriginAddress, nNOPCount);
    if (!result) {
        handleError("CodeCave", result);
    }
}

void MemoryAdapter::WriteByteArray(DWORD dwOriginAddress, unsigned char* ucValue, const int ucValueSize) {
    auto result = SafeWriteByteArray(dwOriginAddress, ucValue, ucValueSize);
    if (!result) {
        handleError("WriteByteArray", result);
    }
}

// 安全版本方法实现
Result<void> MemoryAdapter::SafeSetHook(bool attach, void** ptrTarget, void* ptrDetour) {
    try {
        return getMemoryPatcher().setHook(attach, ptrTarget, ptrDetour);
    }
    catch (const std::exception& e) {
        return Result<void>("Memory manager not initialized: " + std::string(e.what()));
    }
}

Result<void> MemoryAdapter::SafeFillBytes(DWORD dwOriginAddress, unsigned char ucValue, int nCount) {
    try {
        return getMemoryPatcher().fillBytes(static_cast<Address>(dwOriginAddress), ucValue, static_cast<size_t>(nCount));
    }
    catch (const std::exception& e) {
        return Result<void>("Memory manager not initialized: " + std::string(e.what()));
    }
}

Result<void> MemoryAdapter::SafeWriteString(DWORD dwOriginAddress, const char* sContent) {
    if (!sContent) {
        return Result<void>("Null string content");
    }
    
    try {
        return getMemoryPatcher().writeString(static_cast<Address>(dwOriginAddress), std::string(sContent));
    }
    catch (const std::exception& e) {
        return Result<void>("Memory manager not initialized: " + std::string(e.what()));
    }
}

Result<void> MemoryAdapter::SafeWriteByte(DWORD dwOriginAddress, unsigned char ucValue) {
    try {
        return getMemoryPatcher().writeByte(static_cast<Address>(dwOriginAddress), static_cast<uint8_t>(ucValue));
    }
    catch (const std::exception& e) {
        return Result<void>("Memory manager not initialized: " + std::string(e.what()));
    }
}

Result<void> MemoryAdapter::SafeWriteShort(DWORD dwOriginAddress, unsigned short usValue) {
    try {
        return getMemoryPatcher().writeShort(static_cast<Address>(dwOriginAddress), static_cast<uint16_t>(usValue));
    }
    catch (const std::exception& e) {
        return Result<void>("Memory manager not initialized: " + std::string(e.what()));
    }
}

Result<void> MemoryAdapter::SafeWriteInt(DWORD dwOriginAddress, unsigned int dwValue) {
    try {
        return getMemoryPatcher().writeInt(static_cast<Address>(dwOriginAddress), static_cast<uint32_t>(dwValue));
    }
    catch (const std::exception& e) {
        return Result<void>("Memory manager not initialized: " + std::string(e.what()));
    }
}

Result<void> MemoryAdapter::SafeWriteDouble(DWORD dwOriginAddress, double dwValue) {
    try {
        return getMemoryPatcher().writeDouble(static_cast<Address>(dwOriginAddress), dwValue);
    }
    catch (const std::exception& e) {
        return Result<void>("Memory manager not initialized: " + std::string(e.what()));
    }
}

Result<void> MemoryAdapter::SafeCodeCave(void* ptrCodeCave, DWORD dwOriginAddress, int nNOPCount) {
    try {
        return getMemoryPatcher().applyCodeCave(ptrCodeCave, static_cast<Address>(dwOriginAddress), nNOPCount);
    }
    catch (const std::exception& e) {
        return Result<void>("Memory manager not initialized: " + std::string(e.what()));
    }
}

Result<void> MemoryAdapter::SafeWriteByteArray(DWORD dwOriginAddress, unsigned char* ucValue, const int ucValueSize) {
    if (!ucValue || ucValueSize <= 0) {
        return Result<void>("Invalid byte array parameters");
    }
    
    try {
        std::vector<uint8_t> data(ucValue, ucValue + ucValueSize);
        return getMemoryPatcher().writeBytes(static_cast<Address>(dwOriginAddress), data);
    }
    catch (const std::exception& e) {
        return Result<void>("Memory manager not initialized: " + std::string(e.what()));
    }
}

// 地址管理相关方法
Result<void> MemoryAdapter::RegisterAddress(const std::string& name, DWORD address, size_t size) {
    try {
        return getAddressManager().registerAddress(name, static_cast<Address>(address), size);
    }
    catch (const std::exception& e) {
        return Result<void>("Memory manager not initialized: " + std::string(e.what()));
    }
}

Result<DWORD> MemoryAdapter::GetRegisteredAddress(const std::string& name) {
    try {
        auto result = getAddressManager().getAddress(name);
        if (!result) {
            return Result<DWORD>(result.getError());
        }
        return Result<DWORD>(static_cast<DWORD>(*result));
    }
    catch (const std::exception& e) {
        return Result<DWORD>("Memory manager not initialized: " + std::string(e.what()));
    }
}

Result<void> MemoryAdapter::ValidateAddress(const std::string& name) {
    try {
        return getAddressManager().validateRegisteredAddress(name);
    }
    catch (const std::exception& e) {
        return Result<void>("Memory manager not initialized: " + std::string(e.what()));
    }
}

// 配置方法
void MemoryAdapter::SetUseVirtualProtect(bool enable) {
    useVirtualProtect_ = enable;
}

bool MemoryAdapter::GetUseVirtualProtect() {
    return useVirtualProtect_;
}

// 错误处理
void MemoryAdapter::SetErrorHandler(std::function<void(const std::string&)> handler) {
    errorHandler_ = handler;
}

void MemoryAdapter::LogError(const std::string& operation, const std::string& error) {
    std::string message = "[MemoryAdapter] " + operation + " failed: " + error;
    
    if (errorHandler_) {
        errorHandler_(message);
    } else {
        std::cerr << message << std::endl;
    }
}

// 内部辅助方法
void MemoryAdapter::handleError(const std::string& operation, const Result<void>& result) {
    if (!result) {
        LogError(operation, result.getError());
    }
}

template<typename T>
T MemoryAdapter::handleErrorWithReturn(const std::string& operation, const Result<T>& result, T defaultValue) {
    if (!result) {
        LogError(operation, result.getError());
        return defaultValue;
    }
    return *result;
}

} // namespace EzorsiaMemory
