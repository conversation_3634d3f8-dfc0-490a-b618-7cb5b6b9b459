#pragma once
#include <cstdint>

/**
 * @file GameConstants.h
 * @brief 游戏相关常量定义
 * 
 * 此文件包含所有游戏相关的常量定义，包括内存地址、数值常量、配置默认值等。
 * 将原本分散在各个文件中的魔法数字集中管理，提高代码可维护性。
 */

namespace GameConstants {

    // ===== 内存地址常量 =====
    namespace Addresses {
        // DLL注入和基础功能地址
        constexpr uint32_t DINPUT8_DLL_INJECT = 0x00796357;
        constexpr uint32_t MOVEMENT_FLUSH_INTERVAL = 0x0068A83F;
        constexpr uint32_t STAT_WND_ON_STAT_CHANGE = 0x00A20213;
        constexpr uint32_t USER_EMOTE_COOL_TIME = 0x00A244AE;
        constexpr uint32_t USER_GIVE_POPULARITY_COOL_TIME = 0x00A23F28;
        constexpr uint32_t MESSAGE_CHAT_DELAY = 0x00490651;
        constexpr uint32_t MESSAGE_CHAT_SPAM = 0x00490607;
        constexpr uint32_t REMOTE_ADDRESS = 0x00AFE084;
        
        // 加密和安全相关地址
        constexpr uint32_t IG_CIPHER_HASH = 0x00A4A845;
        constexpr uint32_t IG_CIPHER_VIRTUAL1 = 0x00A4A8DA;
        constexpr uint32_t IG_CIPHER_VIRTUAL2 = 0x00A4A9BF;
        constexpr uint32_t IG_CIPHER_DECRYPT = 0x00A4A942;
        constexpr uint32_t IG_CIPHER_DECRYPT_STR = 0x00A4A9F7;
        
        // 游戏机制相关地址
        constexpr uint32_t UNLIMITED_SOUL_RUSH = 0x0096BF09;
        constexpr uint32_t UNLIMITED_FJ = 0x0096BEB5;
        constexpr uint32_t UNLIMITED_FJ_Y_VECTOR = 0x0096BF86;
        
        // 分辨率和UI相关地址
        constexpr uint32_t AVATAR_MEGA_H_POS = 0x0045B97E;
        constexpr uint32_t AVATAR_MEGA_WIDTH = 0x0045A5CB;
        constexpr uint32_t APPLICATION_HEIGHT = 0x009F7B1D;
        constexpr uint32_t APPLICATION_WIDTH = 0x009F7B23;
        constexpr uint32_t CURSOR_VECTOR_V_POS = 0x0059A15D;
        constexpr uint32_t CURSOR_VECTOR_H_POS = 0x0059A169;
        constexpr uint32_t UPDATE_MOUSE_LIMIT_V_POS = 0x0059AC22;
        constexpr uint32_t UPDATE_MOUSE_LIMIT_H_POS = 0x0059AC09;
        
        // 登录相关地址
        constexpr uint32_t LOGIN_SEND_CHECK_PASSWORD_PACKET = 0x005F6B87;
        constexpr uint32_t LOGIN_SECURITY_BYPASS1 = 0x005F6BA0;
        constexpr uint32_t LOGIN_SECURITY_BYPASS2 = 0x005F6BA4;
        constexpr uint32_t SECURITY_CLIENT_ON_PACKET = 0x00496633;
        
        // 游戏启动相关地址
        constexpr uint32_t GAME_STARTUP_FIX = 0x00A63FF3;
        constexpr uint32_t SHOW_STARTUP_WND_MODAL = 0x009F1C04;
        constexpr uint32_t SHOW_AD_BALLOON = 0x009F242F;
        constexpr uint32_t CREATE_MAIN_WINDOW = 0x009F6EDC;
        constexpr uint32_t INITIALIZE_RES_MAN = 0x009F74EA;
        
        // 权限和安全相关地址
        constexpr uint32_t ELEVATION_REQUEST1 = 0x00C08459;
        constexpr uint32_t ELEVATION_REQUEST2 = 0x0049C2CD;
        constexpr uint32_t ELEVATION_REQUEST3 = 0x0049CFE8;
        constexpr uint32_t ELEVATION_REQUEST4 = 0x0049D398;
        constexpr uint32_t FOUR_GB_EDIT = 0x0040013E;
        
        // 实验性功能地址
        constexpr uint32_t EXPERIMENTAL_MISS_RATE1 = 0x00AFE900;
        constexpr uint32_t EXPERIMENTAL_MISS_RATE2 = 0x00AFE908;
        
        // 鼠标滚轮修复地址
        constexpr uint32_t MOUSE_WHEEL_FIX = 0x009E8090;
        
        // 中文支持相关地址
        constexpr uint32_t DATE_FORMAT_FIX1 = 0x008EBF57;
        constexpr uint32_t DATE_FORMAT_FIX2 = 0x008EBFA1;
        constexpr uint32_t DATE_FORMAT_FIX3 = 0x008EC31A;
        constexpr uint32_t DATE_FORMAT_FIX4 = 0x008EBF05;
        constexpr uint32_t FONT_SIZE_FIX1 = 0x008E55ED;
        constexpr uint32_t FONT_SIZE_FIX2 = 0x008E557A;
        constexpr uint32_t FONT_SIZE_FIX3 = 0x008E565E;
        constexpr uint32_t PLAYER_CARD_JOB_POS = 0x0090142E;
        constexpr uint32_t PLAYER_CARD_JOB_SIZE = 0x00901400;
        
        // 装备相关崩溃修复地址
        constexpr uint32_t EQUIPMENT_CANVAS_CRASH_FIX = 0x00401D52;
    }
    
    // ===== 数值常量 =====
    namespace Values {
        // 登录相关补丁值
        constexpr uint8_t LOGIN_PATCH_VALUE = 0x08;
        constexpr uint8_t SECURITY_BYPASS_VALUE1 = 0xA0;
        constexpr uint8_t SECURITY_CLIENT_PATCH_VALUE = 0x47;
        
        // 游戏启动相关补丁值
        constexpr uint8_t SHOW_AD_BALLOON_PATCH = 0xEB;
        constexpr uint8_t CREATE_MAIN_WINDOW_PATCH = 0xEB;
        constexpr uint8_t FOUR_GB_PATCH_VALUE = 0x2F;
        
        // 权限相关补丁值
        constexpr uint8_t ELEVATION_PATCH1 = 0x22;
        constexpr uint8_t ELEVATION_PATCH2 = 0x01;
        
        // 中文支持相关数值
        constexpr uint8_t CHINESE_FONT_SIZE = 0x0B;
        constexpr uint8_t PLAYER_CARD_Y_OFFSET = 0x5E;
        constexpr uint8_t PLAYER_CARD_SIZE = 1;
        
        // 实验性功能数值
        constexpr double EXPERIMENTAL_MISS_RATE = 100.0;
    }
    
    // ===== 配置默认值 =====
    namespace Defaults {
        // 游戏窗口配置
        constexpr int GAME_WIDTH = 1280;
        constexpr int GAME_HEIGHT = 720;
        constexpr bool WINDOWED_MODE = true;
        constexpr bool REMOVE_LOGOS = true;
        constexpr int MSG_AMOUNT = 26;
        
        // 网络配置
        constexpr char SERVER_IP[] = "127.0.0.1";
        constexpr int SERVER_PORT = 8484;
        constexpr char LANGUAGE[] = "CN";
        
        // 游戏机制配置
        constexpr double DAMAGE_CAP = 199999.0;
        constexpr int SPEED_MOVEMENT_CAP = 140;
        constexpr uint32_t JUMP_CAP = 123;
        constexpr float CLIMB_SPEED = 1.0f;
        
        // 功能开关默认值
        constexpr bool USE_TUBI = false;
        constexpr bool CLIMB_SPEED_AUTO = false;
        constexpr bool EXTRA_QUICKSLOTS = false;
        constexpr bool MEMORY_OPTIMIZE = false;
        constexpr int MEMORY_OPTIMIZE_DELAY = 0;
        constexpr bool IN_OUT_LINK_SUPPORT = true;
        constexpr bool EXPERIMENTAL_FEATURE = false;
        constexpr bool SHOW_DEBUG_CONSOLE = false;
        constexpr bool USE_CIL = false;
        
        // 自定义DLL配置
        constexpr char CUSTOM_DLL_1[] = "CUSTOM.dll";
        constexpr char CUSTOM_DLL_2[] = "CUSTOM2.dll";
        constexpr char CUSTOM_DLL_3[] = "CUSTOM3.dll";
        
        // 调试配置
        constexpr int SLEEP_TIME = 0;
    }
    
    // ===== 文件路径常量 =====
    namespace FilePaths {
        constexpr char CONFIG_FILE[] = "config.ini";
        constexpr char BASE_WZ[] = "Base.wz";
        constexpr char EZORSIA_UI_WZ[] = "EzorsiaV2_UI.wz";
        constexpr char ZMAP_IMG[] = "Data/zmap.img";
        constexpr char EZORSIA_IMG[] = "Data/MapleEzorsiaV2wzfiles.img";
    }
    
    // ===== 字符串常量 =====
    namespace Strings {
        // 错误消息
        constexpr wchar_t CONFIG_ERROR_MESSAGE[] = 
            L"your config.ini file cannot be properly read, go to troubleshooting section of "
            L"Ezorsia v2 setup guide at https://github.com/444Ro666/MapleEzorsia-v2 for more details, "
            L"or delete your config.ini to have a new one generated with default settings";
        constexpr wchar_t CONFIG_ERROR_TITLE[] = L"bad config file";
        
        // 调试消息
        constexpr char DINPUT8_HOOK_INITIALIZED[] = "dinput8 hook initialized";
        constexpr char EQUIPMENT_CRASH_FIX_ENABLED[] = "Enable fix some equipment not canvas crash";
        constexpr char EXTRA_QUICKSLOTS_ENABLED[] = "Enable extra quickslot option...";
        
        // 权限相关字符串
        constexpr char AS_INVOKER[] = "asInvoker";
    }
    
    // ===== 数组大小常量 =====
    namespace ArraySizes {
        constexpr size_t LOGIN_SECURITY_PATCH_SIZE = 7;
        constexpr size_t ELEVATION_REQUEST_SIZE = 0x00C0846E - 0x00C08459;
        constexpr int MOUSE_WHEEL_FIX_NOPS = 5;
        constexpr int DATE_FORMAT_FIX_NOPS = 14;
        constexpr int EQUIPMENT_CRASH_FIX_NOPS = 6;
    }

} // namespace GameConstants
