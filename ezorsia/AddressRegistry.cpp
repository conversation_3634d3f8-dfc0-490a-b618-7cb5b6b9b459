#include "stdafx.h"
#include "AddressRegistry.h"
#include <iostream>

namespace EzorsiaMemory {

// 地址映射表定义
namespace AddressMappings {
    
    // 内存相关地址
    const AddressMapping MEMORY_ADDRESSES[] = {
        {"DINPUT8_DLL_INJECT", GameConstants::Addresses::DINPUT8_DLL_INJECT, 4, "DInput8 DLL injection point"},
        {"MOVEMENT_FLUSH_INTERVAL", GameConstants::Addresses::MOVEMENT_FLUSH_INTERVAL, 4, "Movement flush interval address"},
        {"SHOW_STARTUP_WND_MODAL", GameConstants::Addresses::SHOW_STARTUP_WND_MODAL, 5, "Startup window modal display"},
        {"SHOW_AD_BALLOON", GameConstants::Addresses::SHOW_AD_BALLOON, 1, "Advertisement balloon display"},
        {"CREATE_MAIN_WINDOW", GameConstants::Addresses::CREATE_MAIN_WINDOW, 1, "Main window creation"},
        {"INITIALIZE_RES_MAN", GameConstants::Addresses::INITIALIZE_RES_MAN, 4, "Resource manager initialization"},
    };
    const size_t MEMORY_ADDRESS_COUNT = sizeof(MEMORY_ADDRESSES) / sizeof(AddressMapping);
    
    // UI相关地址
    const AddressMapping UI_ADDRESSES[] = {
        {"UI_STATUSBAR_X", 0x0045B337, 4, "Status bar X position"},
        {"UI_STATUSBAR_WIDTH", 0x0045B417, 4, "Status bar width calculation"},
        {"UI_CHAT_HEIGHT", 0x007C2531, 4, "Chat window height"},
        {"UI_EXP_MSG_HEIGHT", 0x0089B796, 4, "Experience message height"},
        {"UI_BOSS_BAR_WIDTH1", 0x00533B03, 4, "Boss bar width extension 1"},
        {"UI_BOSS_BAR_WIDTH2", 0x00534370, 4, "Boss bar width extension 2"},
    };
    const size_t UI_ADDRESS_COUNT = sizeof(UI_ADDRESSES) / sizeof(AddressMapping);
    
    // 游戏玩法相关地址
    const AddressMapping GAMEPLAY_ADDRESSES[] = {
        {"DAMAGE_CAP_DOUBLE", 0x00AFE8A0, 8, "Damage cap (double value)"},
        {"DAMAGE_CAP_INT", 0x008C3304, 4, "Damage cap (integer value)"},
        {"TUBI_ENABLE", 0x00485C32, 2, "Tubi feature enable"},
        {"JUMP_CAP_HOOK1", 0x004031AA, 5, "Jump capability hook point 1"},
        {"JUMP_CAP_HOOK2", 0x00777347, 5, "Jump capability hook point 2"},
        {"JUMP_CAP_HOOK3", 0x004CA089, 6, "Jump capability hook point 3"},
    };
    const size_t GAMEPLAY_ADDRESS_COUNT = sizeof(GAMEPLAY_ADDRESSES) / sizeof(AddressMapping);
    
    // 网络相关地址
    const AddressMapping NETWORK_ADDRESSES[] = {
        {"LOCALHOST_PATCH1", 0x008D54A6, 9, "Localhost patch point 1"},
        {"LOCALHOST_PATCH2", 0x00937225, 9, "Localhost patch point 2"},
        {"LOCALHOST_PATCH3", 0x00531EE8, 9, "Localhost patch point 3"},
        {"CLIPBOARD_SUPPORT1", 0x004CAE7D, 2, "Clipboard Chinese support 1"},
        {"CLIPBOARD_SUPPORT2", 0x004CAE8F, 1, "Clipboard Chinese support 2"},
    };
    const size_t NETWORK_ADDRESS_COUNT = sizeof(NETWORK_ADDRESSES) / sizeof(AddressMapping);
    
    // 系统相关地址
    const AddressMapping SYSTEM_ADDRESSES[] = {
        {"HEAP_CREATE_HOOK", 0x00A6715F, 4, "HeapCreate hook return address"},
        {"MAP_STRUCT_HOOK", 0x004031AA, 5, "Map structure hook"},
        {"ENV_SWITCH_HOOK", 0x00777347, 5, "Environment switch hook"},
        {"CHARACTER_NAME_CHECK", 0x007A015D, 2, "Character name Chinese check"},
        {"IME_DESTROY_WINDOW", 0x004DFEA4, 9, "IME destroy window hook"},
        {"IME_MULTILINE", 0x004D32D9, 7, "IME multiline input hook"},
    };
    const size_t SYSTEM_ADDRESS_COUNT = sizeof(SYSTEM_ADDRESSES) / sizeof(AddressMapping);
}

// AddressRegistry方法实现
Result<void> AddressRegistry::registerAllGameAddresses() {
    std::cout << "[AddressRegistry] Starting registration of all game addresses..." << std::endl;
    
    int totalSuccess = 0;
    int totalCount = 0;
    
    // 注册各类地址
    auto memResult = registerMemoryAddresses();
    auto uiResult = registerUIAddresses();
    auto gameplayResult = registerGameplayAddresses();
    auto networkResult = registerNetworkAddresses();
    auto systemResult = registerSystemAddresses();
    
    // 统计结果
    totalCount = AddressMappings::MEMORY_ADDRESS_COUNT + 
                 AddressMappings::UI_ADDRESS_COUNT + 
                 AddressMappings::GAMEPLAY_ADDRESS_COUNT + 
                 AddressMappings::NETWORK_ADDRESS_COUNT + 
                 AddressMappings::SYSTEM_ADDRESS_COUNT;
    
    // 检查是否有失败的注册
    std::vector<std::string> errors;
    if (!memResult) errors.push_back("Memory: " + memResult.getError());
    if (!uiResult) errors.push_back("UI: " + uiResult.getError());
    if (!gameplayResult) errors.push_back("Gameplay: " + gameplayResult.getError());
    if (!networkResult) errors.push_back("Network: " + networkResult.getError());
    if (!systemResult) errors.push_back("System: " + systemResult.getError());
    
    if (!errors.empty()) {
        std::string combinedError = "Registration failures: ";
        for (size_t i = 0; i < errors.size(); ++i) {
            if (i > 0) combinedError += "; ";
            combinedError += errors[i];
        }
        return Result<void>(combinedError);
    }
    
    std::cout << "[AddressRegistry] Successfully registered " << totalCount << " addresses" << std::endl;
    return Result<void>();
}

Result<void> AddressRegistry::registerMemoryAddresses() {
    int successCount = 0;
    
    for (size_t i = 0; i < AddressMappings::MEMORY_ADDRESS_COUNT; ++i) {
        const auto& mapping = AddressMappings::MEMORY_ADDRESSES[i];
        auto result = registerAddress(mapping.name, mapping.address, mapping.size, mapping.description);
        if (result) {
            successCount++;
        }
    }
    
    logRegistration("Memory", successCount, static_cast<int>(AddressMappings::MEMORY_ADDRESS_COUNT));
    
    if (successCount != AddressMappings::MEMORY_ADDRESS_COUNT) {
        return Result<void>("Failed to register some memory addresses");
    }
    
    return Result<void>();
}

Result<void> AddressRegistry::registerUIAddresses() {
    int successCount = 0;
    
    for (size_t i = 0; i < AddressMappings::UI_ADDRESS_COUNT; ++i) {
        const auto& mapping = AddressMappings::UI_ADDRESSES[i];
        auto result = registerAddress(mapping.name, mapping.address, mapping.size, mapping.description);
        if (result) {
            successCount++;
        }
    }
    
    logRegistration("UI", successCount, static_cast<int>(AddressMappings::UI_ADDRESS_COUNT));
    
    if (successCount != AddressMappings::UI_ADDRESS_COUNT) {
        return Result<void>("Failed to register some UI addresses");
    }
    
    return Result<void>();
}

Result<void> AddressRegistry::registerGameplayAddresses() {
    int successCount = 0;
    
    for (size_t i = 0; i < AddressMappings::GAMEPLAY_ADDRESS_COUNT; ++i) {
        const auto& mapping = AddressMappings::GAMEPLAY_ADDRESSES[i];
        auto result = registerAddress(mapping.name, mapping.address, mapping.size, mapping.description);
        if (result) {
            successCount++;
        }
    }
    
    logRegistration("Gameplay", successCount, static_cast<int>(AddressMappings::GAMEPLAY_ADDRESS_COUNT));
    
    if (successCount != AddressMappings::GAMEPLAY_ADDRESS_COUNT) {
        return Result<void>("Failed to register some gameplay addresses");
    }
    
    return Result<void>();
}

Result<void> AddressRegistry::registerNetworkAddresses() {
    int successCount = 0;
    
    for (size_t i = 0; i < AddressMappings::NETWORK_ADDRESS_COUNT; ++i) {
        const auto& mapping = AddressMappings::NETWORK_ADDRESSES[i];
        auto result = registerAddress(mapping.name, mapping.address, mapping.size, mapping.description);
        if (result) {
            successCount++;
        }
    }
    
    logRegistration("Network", successCount, static_cast<int>(AddressMappings::NETWORK_ADDRESS_COUNT));
    
    if (successCount != AddressMappings::NETWORK_ADDRESS_COUNT) {
        return Result<void>("Failed to register some network addresses");
    }
    
    return Result<void>();
}

Result<void> AddressRegistry::registerSystemAddresses() {
    int successCount = 0;
    
    for (size_t i = 0; i < AddressMappings::SYSTEM_ADDRESS_COUNT; ++i) {
        const auto& mapping = AddressMappings::SYSTEM_ADDRESSES[i];
        auto result = registerAddress(mapping.name, mapping.address, mapping.size, mapping.description);
        if (result) {
            successCount++;
        }
    }
    
    logRegistration("System", successCount, static_cast<int>(AddressMappings::SYSTEM_ADDRESS_COUNT));
    
    if (successCount != AddressMappings::SYSTEM_ADDRESS_COUNT) {
        return Result<void>("Failed to register some system addresses");
    }
    
    return Result<void>();
}

Result<void> AddressRegistry::validateAllRegisteredAddresses() {
    std::cout << "[AddressRegistry] Validating all registered addresses..." << std::endl;
    
    try {
        auto& manager = getAddressManager();
        auto names = manager.getRegisteredAddressNames();
        
        int validCount = 0;
        int totalCount = static_cast<int>(names.size());
        
        for (const auto& name : names) {
            auto result = manager.validateRegisteredAddress(name);
            if (result) {
                validCount++;
            } else {
                std::cerr << "[AddressRegistry] Validation failed for '" << name << "': " << result.getError() << std::endl;
            }
        }
        
        std::cout << "[AddressRegistry] Validation complete: " << validCount << "/" << totalCount << " addresses valid" << std::endl;
        
        if (validCount != totalCount) {
            return Result<void>("Some addresses failed validation");
        }
        
        return Result<void>();
    }
    catch (const std::exception& e) {
        return Result<void>("Address validation failed: " + std::string(e.what()));
    }
}

void AddressRegistry::printRegistrationStats() {
    try {
        auto& manager = getAddressManager();
        auto names = manager.getRegisteredAddressNames();
        
        std::cout << "\n[AddressRegistry] Registration Statistics:" << std::endl;
        std::cout << "  Total registered addresses: " << names.size() << std::endl;
        std::cout << "  Memory addresses: " << AddressMappings::MEMORY_ADDRESS_COUNT << std::endl;
        std::cout << "  UI addresses: " << AddressMappings::UI_ADDRESS_COUNT << std::endl;
        std::cout << "  Gameplay addresses: " << AddressMappings::GAMEPLAY_ADDRESS_COUNT << std::endl;
        std::cout << "  Network addresses: " << AddressMappings::NETWORK_ADDRESS_COUNT << std::endl;
        std::cout << "  System addresses: " << AddressMappings::SYSTEM_ADDRESS_COUNT << std::endl;
        
        // 显示前10个注册的地址作为示例
        std::cout << "\n  Sample registered addresses:" << std::endl;
        for (size_t i = 0; i < std::min(names.size(), size_t(10)); ++i) {
            auto addrResult = manager.getAddress(names[i]);
            if (addrResult) {
                std::cout << "    " << names[i] << ": 0x" << std::hex << *addrResult << std::dec << std::endl;
            }
        }
        
        if (names.size() > 10) {
            std::cout << "    ... and " << (names.size() - 10) << " more" << std::endl;
        }
        std::cout << std::endl;
    }
    catch (const std::exception& e) {
        std::cerr << "[AddressRegistry] Failed to print stats: " << e.what() << std::endl;
    }
}

// 私有辅助方法
Result<void> AddressRegistry::registerAddress(const std::string& name, uint32_t address, size_t size, const std::string& description) {
    try {
        auto& manager = getAddressManager();
        return manager.registerAddress(name, static_cast<Address>(address), size);
    }
    catch (const std::exception& e) {
        return Result<void>("Failed to register address '" + name + "': " + std::string(e.what()));
    }
}

void AddressRegistry::logRegistration(const std::string& category, int successCount, int totalCount) {
    std::cout << "[AddressRegistry] " << category << " addresses: " << successCount << "/" << totalCount;
    if (successCount == totalCount) {
        std::cout << " ✓" << std::endl;
    } else {
        std::cout << " ✗" << std::endl;
    }
}

} // namespace EzorsiaMemory
