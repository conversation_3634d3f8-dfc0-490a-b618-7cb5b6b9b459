#pragma once
#include <cstdint>
#include <vector>
#include <memory>
#include <string>
#include <optional>

namespace EzorsiaMemory {

// 类型别名
using Address = uintptr_t;
using ProtectionFlags = uint32_t;

// 结果类型，用于错误处理
template<typename T>
class Result {
private:
    std::optional<T> value_;
    std::string error_;
    bool success_;

public:
    Result(T&& val) : value_(std::move(val)), success_(true) {}
    Result(const T& val) : value_(val), success_(true) {}
    Result(const std::string& error) : error_(error), success_(false) {}
    
    bool isSuccess() const { return success_; }
    bool isError() const { return !success_; }
    
    const T& getValue() const { 
        if (!success_) throw std::runtime_error("Accessing value of failed result: " + error_);
        return *value_; 
    }
    
    const std::string& getError() const { return error_; }
    
    // 便捷操作符
    explicit operator bool() const { return success_; }
    const T& operator*() const { return getValue(); }
    const T* operator->() const { return &getValue(); }
};

// 特化void类型的Result
template<>
class Result<void> {
private:
    std::string error_;
    bool success_;

public:
    Result() : success_(true) {}
    Result(const std::string& error) : error_(error), success_(false) {}
    
    bool isSuccess() const { return success_; }
    bool isError() const { return !success_; }
    const std::string& getError() const { return error_; }
    
    explicit operator bool() const { return success_; }
};

// 内存保护标志
namespace Protection {
    constexpr ProtectionFlags READ = 0x01;
    constexpr ProtectionFlags WRITE = 0x02;
    constexpr ProtectionFlags EXECUTE = 0x04;
    constexpr ProtectionFlags READ_WRITE = READ | WRITE;
    constexpr ProtectionFlags READ_EXECUTE = READ | EXECUTE;
    constexpr ProtectionFlags READ_WRITE_EXECUTE = READ | WRITE | EXECUTE;
}

// 地址验证器接口
class IAddressValidator {
public:
    virtual ~IAddressValidator() = default;
    virtual Result<void> validateAddress(Address addr, size_t size) const = 0;
    virtual Result<void> validateExecutableAddress(Address addr) const = 0;
};

// 内存补丁器接口
class IMemoryPatcher {
public:
    virtual ~IMemoryPatcher() = default;
    
    // 基础内存写入操作
    virtual Result<void> writeByte(Address addr, uint8_t value) = 0;
    virtual Result<void> writeShort(Address addr, uint16_t value) = 0;
    virtual Result<void> writeInt(Address addr, uint32_t value) = 0;
    virtual Result<void> writeDouble(Address addr, double value) = 0;
    virtual Result<void> writeString(Address addr, const std::string& value) = 0;
    virtual Result<void> writeBytes(Address addr, const std::vector<uint8_t>& data) = 0;
    
    // 内存填充操作
    virtual Result<void> fillBytes(Address addr, uint8_t value, size_t count) = 0;
    
    // 代码洞穴操作
    virtual Result<void> applyCodeCave(void* caveFunction, Address targetAddr, int nopCount) = 0;
    
    // 函数钩子操作
    virtual Result<void> setHook(bool attach, void** targetFunc, void* detourFunc) = 0;
    
    // 内存保护操作
    virtual Result<ProtectionFlags> changeProtection(Address addr, size_t size, ProtectionFlags newProtection) = 0;
    virtual Result<void> restoreProtection(Address addr, size_t size, ProtectionFlags oldProtection) = 0;
};

// 地址管理器接口
class IAddressManager {
public:
    virtual ~IAddressManager() = default;
    
    // 地址注册和验证
    virtual Result<void> registerAddress(const std::string& name, Address addr, size_t size = 1) = 0;
    virtual Result<Address> getAddress(const std::string& name) const = 0;
    virtual Result<void> validateRegisteredAddress(const std::string& name) const = 0;
    
    // 地址范围检查
    virtual Result<void> validateAddressRange(Address addr, size_t size) const = 0;
    virtual bool isValidExecutableAddress(Address addr) const = 0;
    
    // 地址信息查询
    virtual std::vector<std::string> getRegisteredAddressNames() const = 0;
    virtual size_t getRegisteredAddressCount() const = 0;
};

// 内存管理器工厂
class MemoryManagerFactory {
public:
    static std::unique_ptr<IAddressValidator> createAddressValidator();
    static std::unique_ptr<IMemoryPatcher> createMemoryPatcher(
        std::shared_ptr<IAddressValidator> validator = nullptr,
        bool useVirtualProtect = true
    );
    static std::unique_ptr<IAddressManager> createAddressManager(
        std::shared_ptr<IAddressValidator> validator = nullptr
    );
};

// 全局访问接口
IMemoryPatcher& getMemoryPatcher();
IAddressManager& getAddressManager();

// 初始化和清理
Result<void> initializeMemoryManager(bool useVirtualProtect = true);
void shutdownMemoryManager();

} // namespace EzorsiaMemory
