#pragma once

class Client {
	public:
		static void UpdateGameStartup();
		static void EnableNewIGCipher();
		static void UpdateResolution();
		static void UpdateLogin();
		static void JumpCap();
		static const int m_nIGCipherHash = 0XC65053F2;
		static int m_nGameHeight;
		static int m_nGameWidth;
		static int MsgAmount;
		static bool WindowedMode;
		static bool RemoveLogos;
		static double setDamageCap;
		static bool useTubi;
		static int speedMovementCap;
		static std::string ServerIP_AddressFromINI;
		static int serverPort;
		static std::string language;
		static bool climbSpeedAuto;
		static float climbSpeed;
		static DWORD jumpCap;
		static bool extraQuickslots;
		static bool memoryOptimize;
		static int memoryOptimizeDelay;
		static bool InOutLinkSupport;
		static bool experimentalFeature;
};