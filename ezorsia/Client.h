#pragma once

class Client {
	public:
		static void updateGameStartup();
		static void enableNewIGCipher();
		static void updateResolution();
		static void updateLogin();
		static void jumpCap();

		// 游戏配置常量
		static const int igCipherHash = 0XC65053F2;

		// 游戏配置变量
		static int gameHeight;
		static int gameWidth;
		static int msgAmount;
		static bool windowedMode;
		static bool removeLogos;
		static double damageCap;
		static bool useTubi;
		static int speedMovementCap;
		static std::string serverIPAddress;
		static int serverPort;
		static std::string language;
		static bool climbSpeedAuto;
		static float climbSpeed;
		static uint32_t jumpCap;
		static bool extraQuickslots;
		static bool memoryOptimize;
		static int memoryOptimizeDelay;
		static bool inOutLinkSupport;
		static bool experimentalFeature;

	private:
		// 重构后的内部方法 - 将UpdateGameStartup拆分为职责单一的小函数
		static void applyGameStartupPatches();
		static void applyLoginSecurityPatches();
		static void applyUIPatches();
		static void applySecurityPatches();
		static void applyLanguagePatches();
		static void applyExperimentalPatches();
		static void applyMiscPatches();
};