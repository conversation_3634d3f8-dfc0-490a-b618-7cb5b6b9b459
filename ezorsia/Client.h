#pragma once
#include "ConfigManager.h"

class Client {
	public:
		static void updateGameStartup();
		static void enableNewIGCipher();
		static void updateResolution();
		static void updateLogin();
		static void removeJumpCap();
        static void update();

		// 游戏配置常量
		static const int igCipherHash = 0XC65053F2;

		// 配置管理器访问方法
		static void initializeConfig(const std::string& configPath = "config.ini");
		static const EzorsiaConfig::EzorsiaConfiguration& getConfig();

		// 便捷访问方法 - 显示配置
		static int getGameHeight();
		static int getGameWidth();
		static int getMsgAmount();
		static bool isWindowedMode();
		static bool shouldRemoveLogos();

		// 便捷访问方法 - 网络配置
		static const std::string& getServerIP();
		static int getServerPort();
		static const std::string& getLanguage();

		// 便捷访问方法 - 游戏机制配置
		static double getDamageCap();
		static bool shouldUseTubi();
		static int getSpeedMovementCap();
		static uint32_t getJumpCap();
		static bool isClimbSpeedAuto();
		static float getClimbSpeed();

		// 便捷访问方法 - 可选功能配置
		static bool hasExtraQuickslots();
		static bool shouldMemoryOptimize();
		static int getMemoryOptimizeDelay();
		static bool hasInOutLinkSupport();
		static bool hasExperimentalFeature();

	private:
		// 重构后的内部方法 - 将updateGameStartup拆分为职责单一的小函数
		static void applyGameStartupPatches();
		static void applyLoginSecurityPatches();
		static void applyUIPatches();
		static void applySecurityPatches();
		static void applyLanguagePatches();
		static void applyExperimentalPatches();
		static void applyMiscPatches();
};