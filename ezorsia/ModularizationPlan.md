# 模块化文件结构重组计划

## 当前问题分析

### 1. 文件组织混乱
- 所有文件都在根目录下，缺乏逻辑分组
- 相关功能分散在不同文件中
- 缺乏清晰的模块边界

### 2. 依赖关系复杂
- Memory相关文件：Memory.h/cpp, MemoryAdapter.h/cpp, MemoryManager.h/cpp
- 配置管理：ConfigManager.h/cpp, Client.h/cpp
- 游戏功能：Client.cpp, FixIme.h, PetEx.h, CIL.h
- 测试文件：MemoryManagerTest.h/cpp, MemoryRefactorTest.h/cpp
- 工具类：INIReader.h, i18n.h, AutoTypes.h

### 3. 循环依赖风险
- Client.h 依赖 ConfigManager.h
- Memory.cpp 依赖 MemoryAdapter.h
- MemoryAdapter.h 依赖 MemoryManager.h

## 模块化重组方案

### 新的目录结构
```
ezorsia/
├── core/                    # 核心系统模块
│   ├── memory/             # 内存管理模块
│   │   ├── MemoryManager.h
│   │   ├── MemoryManager.cpp
│   │   ├── MemoryAdapter.h
│   │   ├── MemoryAdapter.cpp
│   │   ├── AddressRegistry.h
│   │   ├── AddressRegistry.cpp
│   │   └── Memory.h         # 兼容性层
│   │   └── Memory.cpp
│   ├── config/             # 配置管理模块
│   │   ├── ConfigManager.h
│   │   ├── ConfigManager.cpp
│   │   └── INIReader.h
│   └── types/              # 类型定义模块
│       ├── AutoTypes.h
│       ├── GameConstants.h
│       └── CommonTypes.h
├── client/                 # 客户端功能模块
│   ├── Client.h
│   ├── Client.cpp
│   ├── ClientPatches.h     # 新增：客户端补丁管理
│   ├── ClientPatches.cpp
│   └── features/           # 客户端功能子模块
│       ├── FixIme.h
│       ├── PetEx.h
│       ├── CIL.h
│       └── Resman.h
├── system/                 # 系统集成模块
│   ├── dllmain.cpp
│   ├── MainMain.h
│   ├── MainMain.cpp
│   ├── dinput8.h
│   └── dinput8.cpp
├── utils/                  # 工具模块
│   ├── i18n.h
│   ├── codecaves.h
│   ├── ReplacementFuncs.h
│   ├── HeapCreateEx.h
│   └── syelog.h
├── external/               # 外部依赖
│   ├── detours/
│   │   ├── detours.h
│   │   └── detver.h
│   └── maple_types/        # MapleStory类型定义
│       └── MapleClientCollectionTypes/
├── tests/                  # 测试模块
│   ├── memory/
│   │   ├── MemoryManagerTest.h
│   │   ├── MemoryManagerTest.cpp
│   │   ├── MemoryRefactorTest.h
│   │   └── MemoryRefactorTest.cpp
│   └── TestRunner.h
├── migration/              # 迁移工具
│   ├── MemoryMigrationGuide.md
│   ├── MemoryMigrationAnalyzer.h
│   └── MemoryMigrationAnalyzer.cpp
└── resources/              # 资源文件
    ├── resource.h
    ├── stdafx.h
    ├── targetver.h
    └── AddyLocations.h
```

## 模块依赖关系

### 1. 核心模块 (core/)
- **memory/**: 独立的内存管理模块，无外部依赖
- **config/**: 配置管理模块，依赖utils/
- **types/**: 类型定义模块，被其他模块依赖

### 2. 客户端模块 (client/)
- 依赖core/memory, core/config, core/types
- features/子模块依赖client/主模块

### 3. 系统模块 (system/)
- 依赖所有其他模块，作为集成层

### 4. 工具模块 (utils/)
- 独立工具，最小依赖

### 5. 测试模块 (tests/)
- 依赖被测试的模块

## 重组步骤

### 阶段1: 创建目录结构
1. 创建新的目录结构
2. 移动文件到对应目录
3. 更新项目文件(.vcxproj)

### 阶段2: 修复包含路径
1. 更新所有#include语句
2. 修复相对路径引用
3. 更新预编译头文件

### 阶段3: 解决循环依赖
1. 分析依赖关系
2. 重构接口定义
3. 使用前向声明

### 阶段4: 验证和测试
1. 编译验证
2. 运行测试套件
3. 功能验证

## 预期收益

### 1. 清晰的模块边界
- 每个模块职责单一
- 依赖关系明确
- 便于维护和扩展

### 2. 更好的代码组织
- 相关文件集中管理
- 逻辑分组清晰
- 便于导航和理解

### 3. 降低耦合度
- 模块间接口明确
- 减少不必要的依赖
- 提高代码复用性

### 4. 便于测试
- 测试文件集中管理
- 模块化测试策略
- 更好的测试覆盖

## 风险评估

### 1. 编译风险
- 大量文件移动可能导致编译错误
- 需要仔细更新包含路径

### 2. 功能风险
- 文件移动可能影响现有功能
- 需要全面的功能测试

### 3. 维护风险
- 短期内可能增加维护复杂度
- 需要更新文档和注释

## 缓解措施

1. **分阶段执行**: 逐步移动文件，每次验证
2. **备份策略**: 在重组前创建完整备份
3. **测试验证**: 每个阶段都进行编译和功能测试
4. **文档更新**: 及时更新相关文档
