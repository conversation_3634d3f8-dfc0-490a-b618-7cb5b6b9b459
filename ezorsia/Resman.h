#pragma once

#include <OAIdl.h>
#include <memory>
#include <string>
#include <map>

class IWzProperty;

class Resman {
public:
    static Resman& getInstance() {
        static Resman instance;
        return instance;
    }
    static DWORD* GetResManInstance();

    void initialize();
    void cleanup();

private:
    Resman() = default;
    ~<PERSON><PERSON>() = default;
    <PERSON><PERSON>(const Resman&) = delete;
    Resman& operator=(const Resman&) = delete;

    void* getUOLProperty(VARIANT* prop, void** result);
    std::wstring getImgFullPath(std::wstring strT);
    DWORD getCanvasPropertyByPath(std::wstring path, DWORD* result);

    static int __fastcall IWzCanvas_operator_equal_Hook(DWORD* This, void* notuse, DWORD* a2);
    static VARIANTARG* __fastcall IWzResMan__GetObjectA_Hook(DWORD* This, void* notuse, VARIANTARG* pvargDest, int* sUOL, int vParam, int vAux);
    static VARIANTARG* __fastcall IWzProperty__GetItem_Hook(IUnknown* This, void* notuse, VARIANTARG* pvargDest, int* sPath);

    std::map<IUnknown*, std::shared_ptr<std::wstring>> imgPathMap;
    
    static Resman* instance;
}; 


VARIANTARG* getGetObjectAForPath(std::wstring path);
IWzProperty* getIWzPropertyForPath(std::wstring path);
