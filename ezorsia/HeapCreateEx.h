/**
* This code file is sourced from the repository:
 * https://github.com/Willh92/CMS079-ijl15/blob/main/ezorsia/HeapCreateEx.h
 *
 * Please refer to the original repository for the most up-to-date version,
 * licensing information, and additional documentation.
 */

#pragma once

class HeapCreateEx {
public:
    static DWORD GetFuncAddress(LPCSTR lpModule, LPCSTR lpFunc);
	static void HOOK_HeapCreate();
	static void MemoryOptimization();
};