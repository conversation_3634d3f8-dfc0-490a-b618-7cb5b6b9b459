#include "stdafx.h"
#include "ConfigManager.h"
#include "GameConstants.h"
#include <filesystem>
#include <fstream>
#include <iostream>
#include <Windows.h>
#include "resource.h"

namespace EzorsiaConfig {

// ===== INIConfigManager实现 =====

bool INIConfigManager::loadConfig(const std::string& configPath) {
    try {
        // 检查配置文件是否存在
        if (!configFileExists(configPath)) {
            std::cout << "[ConfigManager] Config file not found, creating default: " << configPath << std::endl;
            if (!createDefaultConfigFile(configPath)) {
                lastError_ = "Failed to create default config file";
                return false;
            }
        }
        
        // 创建INI读取器
        reader_ = std::make_unique<INIReader>(configPath);
        
        if (reader_->ParseError() != 0) {
            lastError_ = "Failed to parse config file: " + configPath + 
                        " (Error at line " + std::to_string(reader_->ParseError()) + ")";
            return false;
        }
        
        // 加载各个配置段
        loadDisplayConfig();
        loadNetworkConfig();
        loadMechanicsConfig();
        loadOptionalConfig();
        loadDebugConfig();
        
        // 验证配置
        auto validation = validateConfig();
        if (!validation.isValid) {
            lastError_ = "Configuration validation failed";
            for (const auto& error : validation.errors) {
                std::cout << "[ConfigManager] Validation error: " << error << std::endl;
            }
            return false;
        }
        
        std::cout << "[ConfigManager] Configuration loaded successfully from: " << configPath << std::endl;
        return true;
        
    } catch (const std::exception& e) {
        lastError_ = "Exception while loading config: " + std::string(e.what());
        return false;
    }
}

void INIConfigManager::loadDisplayConfig() {
    config_.display.width = reader_->GetInteger("general", "width", GameConstants::Defaults::GAME_WIDTH);
    config_.display.height = reader_->GetInteger("general", "height", GameConstants::Defaults::GAME_HEIGHT);
    config_.display.windowedMode = reader_->GetBoolean("general", "windowedMode", GameConstants::Defaults::WINDOWED_MODE);
    config_.display.removeLogos = reader_->GetBoolean("general", "removeLogos", GameConstants::Defaults::REMOVE_LOGOS);
    config_.display.msgAmount = reader_->GetInteger("general", "msgAmount", GameConstants::Defaults::MSG_AMOUNT);
}

void INIConfigManager::loadNetworkConfig() {
    config_.network.serverIP = reader_->Get("general", "ServerIP_Address", GameConstants::Defaults::SERVER_IP);
    config_.network.serverPort = reader_->GetInteger("general", "ServerPort", GameConstants::Defaults::SERVER_PORT);
    config_.network.language = reader_->Get("general", "language", GameConstants::Defaults::LANGUAGE);
}

void INIConfigManager::loadMechanicsConfig() {
    config_.mechanics.damageCap = reader_->GetReal("optional", "damageCap", GameConstants::Defaults::DAMAGE_CAP);
    config_.mechanics.useTubi = reader_->GetBoolean("optional", "useTubi", GameConstants::Defaults::USE_TUBI);
    config_.mechanics.speedMovementCap = reader_->GetInteger("optional", "speedMovementCap", GameConstants::Defaults::SPEED_MOVEMENT_CAP);
    config_.mechanics.jumpCap = static_cast<uint32_t>(reader_->GetInteger("optional", "jumpCap", GameConstants::Defaults::JUMP_CAP));
    config_.mechanics.climbSpeedAuto = reader_->GetBoolean("optional", "climbSpeedAuto", GameConstants::Defaults::CLIMB_SPEED_AUTO);
    config_.mechanics.climbSpeed = static_cast<float>(reader_->GetReal("optional", "climbSpeed", GameConstants::Defaults::CLIMB_SPEED));
}

void INIConfigManager::loadOptionalConfig() {
    config_.optional.extraQuickslots = reader_->GetBoolean("optional", "ExtraQuickslots", GameConstants::Defaults::EXTRA_QUICKSLOTS);
    config_.optional.memoryOptimize = reader_->GetBoolean("optional", "MemoryOptimization", GameConstants::Defaults::MEMORY_OPTIMIZE);
    config_.optional.memoryOptimizeDelay = reader_->GetInteger("optional", "MemoryOptimizationDelay", GameConstants::Defaults::MEMORY_OPTIMIZE_DELAY);
    config_.optional.inOutLinkSupport = reader_->GetBoolean("optional", "inOutLinkSupport", GameConstants::Defaults::IN_OUT_LINK_SUPPORT);
    config_.optional.experimentalFeature = reader_->GetBoolean("optional", "experimentalFeature", GameConstants::Defaults::EXPERIMENTAL_FEATURE);
    
    config_.optional.ownCashShopFrame = reader_->GetBoolean("optional", "ownCashShopFrame", false);
    config_.optional.useV62_ExpTable = reader_->GetBoolean("optional", "useV62_ExpTable", false);
    config_.optional.customLoginFrame = reader_->GetBoolean("optional", "CustomLoginFrame", false);
    
    config_.optional.customDll1 = reader_->Get("optional", "use_custom_dll_1", "CUSTOM.dll");
    config_.optional.customDll2 = reader_->Get("optional", "use_custom_dll_2", "CUSTOM2.dll");
    config_.optional.customDll3 = reader_->Get("optional", "use_custom_dll_3", "CUSTOM3.dll");
}

void INIConfigManager::loadDebugConfig() {
    config_.debug.showConsole = reader_->GetBoolean("debug", "showConsole", false);
    config_.debug.sleepTime = reader_->GetInteger("debug", "sleepTime", 0);
}

ValidationResult INIConfigManager::validateConfig() const {
    ValidationResult result;
    
    if (!config_.display.isValid()) {
        result.addError("Invalid display configuration");
    }
    
    if (!config_.network.isValid()) {
        result.addError("Invalid network configuration");
    }
    
    if (!config_.mechanics.isValid()) {
        result.addError("Invalid game mechanics configuration");
    }
    
    if (!config_.optional.isValid()) {
        result.addError("Invalid optional features configuration");
    }
    
    if (!config_.debug.isValid()) {
        result.addError("Invalid debug configuration");
    }
    
    return result;
}

void INIConfigManager::resetToDefaults() {
    config_ = EzorsiaConfiguration{}; // 使用默认构造函数重置
}

bool INIConfigManager::configFileExists(const std::string& configPath) const {
    return std::filesystem::exists(configPath);
}

bool INIConfigManager::createDefaultConfigFile(const std::string& configPath) const {
    try {
        HANDLE hFile = CreateFileA(configPath.c_str(), 
                                  GENERIC_READ | GENERIC_WRITE, 
                                  NULL, NULL, CREATE_ALWAYS, NULL, NULL);
        
        if (hFile == INVALID_HANDLE_VALUE) {
            return false;
        }
        
        // 从资源中加载默认配置
        HMODULE hModule = GetModuleHandle(L"dinput8.dll");
        if (hModule) {
            HRSRC hResource = FindResource(hModule, MAKEINTRESOURCE(IDR_RCDATA1), RT_RCDATA);
            if (hResource) {
                HGLOBAL hResourceData = LoadResource(hModule, hResource);
                DWORD resourceSize = SizeofResource(hModule, hResource);
                LPVOID resourceData = LockResource(hResourceData);
                
                if (resourceData && resourceSize > 0) {
                    DWORD bytesWritten;
                    bool success = WriteFile(hFile, resourceData, resourceSize, &bytesWritten, NULL) &&
                                  (bytesWritten == resourceSize);
                    CloseHandle(hFile);
                    return success;
                }
            }
        }
        
        CloseHandle(hFile);
        return false;
        
    } catch (const std::exception&) {
        return false;
    }
}

bool INIConfigManager::saveConfig(const std::string& configPath) const {
    // 注意：INI文件保存功能需要更复杂的实现
    // 这里先提供基本框架，实际实现可能需要第三方库或自定义INI写入器
    lastError_ = "Save functionality not yet implemented";
    return false;
}

// ===== 全局配置管理器 =====

static std::unique_ptr<INIConfigManager> g_configManager = nullptr;

INIConfigManager& getConfigManager() {
    if (!g_configManager) {
        g_configManager = std::make_unique<INIConfigManager>();
    }
    return *g_configManager;
}

const EzorsiaConfiguration& getConfig() {
    return getConfigManager().getConfig();
}

const GameDisplayConfig& getDisplayConfig() {
    return getConfig().display;
}

const NetworkConfig& getNetworkConfig() {
    return getConfig().network;
}

const GameMechanicsConfig& getMechanicsConfig() {
    return getConfig().mechanics;
}

const OptionalFeaturesConfig& getOptionalConfig() {
    return getConfig().optional;
}

const DebugConfig& getDebugConfig() {
    return getConfig().debug;
}

} // namespace EzorsiaConfig
