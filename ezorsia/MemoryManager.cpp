#include "stdafx.h"
#include "MemoryManager.h"
#include "detours.h"
#include <unordered_map>
#include <mutex>
#include <iostream>

namespace EzorsiaMemory {

// Windows地址验证器实现
class WindowsAddressValidator : public IAddressValidator {
public:
    Result<void> validateAddress(Address addr, size_t size) const override {
        if (addr == 0) {
            return Result<void>("Invalid null address");
        }
        
        // 检查地址是否在有效范围内 (MapleStory v83 通常在 0x00400000 - 0x01000000 范围)
        if (addr < 0x00400000 || addr > 0x01000000) {
            return Result<void>("Address out of valid range: 0x" + std::to_string(addr));
        }
        
        // 使用VirtualQuery检查内存页是否可访问
        MEMORY_BASIC_INFORMATION mbi;
        if (VirtualQuery(reinterpret_cast<LPCVOID>(addr), &mbi, sizeof(mbi)) == 0) {
            return Result<void>("Failed to query memory at address: 0x" + std::to_string(addr));
        }
        
        // 检查内存页状态
        if (mbi.State != MEM_COMMIT) {
            return Result<void>("Memory not committed at address: 0x" + std::to_string(addr));
        }
        
        // 检查大小是否超出页边界
        if (addr + size > reinterpret_cast<Address>(mbi.BaseAddress) + mbi.RegionSize) {
            return Result<void>("Memory operation exceeds page boundary");
        }
        
        return Result<void>();
    }
    
    Result<void> validateExecutableAddress(Address addr) const override {
        auto result = validateAddress(addr, 1);
        if (!result) {
            return result;
        }
        
        MEMORY_BASIC_INFORMATION mbi;
        if (VirtualQuery(reinterpret_cast<LPCVOID>(addr), &mbi, sizeof(mbi)) == 0) {
            return Result<void>("Failed to query memory protection");
        }
        
        // 检查是否可执行
        if (!(mbi.Protect & (PAGE_EXECUTE | PAGE_EXECUTE_READ | PAGE_EXECUTE_READWRITE))) {
            return Result<void>("Address is not executable: 0x" + std::to_string(addr));
        }
        
        return Result<void>();
    }
};

// Windows内存补丁器实现
class WindowsMemoryPatcher : public IMemoryPatcher {
private:
    std::shared_ptr<IAddressValidator> validator_;
    bool useVirtualProtect_;
    mutable std::mutex mutex_;
    
    // RAII内存保护管理器
    class ProtectionGuard {
    private:
        Address addr_;
        size_t size_;
        DWORD oldProtect_;
        bool active_;
        
    public:
        ProtectionGuard(Address addr, size_t size, bool useProtection) 
            : addr_(addr), size_(size), active_(false) {
            if (useProtection) {
                if (VirtualProtect(reinterpret_cast<LPVOID>(addr_), size_, 
                                 PAGE_EXECUTE_READWRITE, &oldProtect_)) {
                    active_ = true;
                }
            }
        }
        
        ~ProtectionGuard() {
            if (active_) {
                DWORD dummy;
                VirtualProtect(reinterpret_cast<LPVOID>(addr_), size_, oldProtect_, &dummy);
            }
        }
        
        bool isActive() const { return active_; }
    };
    
public:
    WindowsMemoryPatcher(std::shared_ptr<IAddressValidator> validator, bool useVirtualProtect)
        : validator_(validator), useVirtualProtect_(useVirtualProtect) {}
    
    Result<void> writeByte(Address addr, uint8_t value) override {
        std::lock_guard<std::mutex> lock(mutex_);
        
        if (validator_) {
            auto validation = validator_->validateAddress(addr, sizeof(uint8_t));
            if (!validation) {
                return validation;
            }
        }
        
        try {
            ProtectionGuard guard(addr, sizeof(uint8_t), useVirtualProtect_);
            if (useVirtualProtect_ && !guard.isActive()) {
                return Result<void>("Failed to change memory protection");
            }
            
            *reinterpret_cast<uint8_t*>(addr) = value;
            return Result<void>();
        }
        catch (const std::exception& e) {
            return Result<void>("Memory write failed: " + std::string(e.what()));
        }
    }
    
    Result<void> writeShort(Address addr, uint16_t value) override {
        std::lock_guard<std::mutex> lock(mutex_);
        
        if (validator_) {
            auto validation = validator_->validateAddress(addr, sizeof(uint16_t));
            if (!validation) {
                return validation;
            }
        }
        
        try {
            ProtectionGuard guard(addr, sizeof(uint16_t), useVirtualProtect_);
            if (useVirtualProtect_ && !guard.isActive()) {
                return Result<void>("Failed to change memory protection");
            }
            
            *reinterpret_cast<uint16_t*>(addr) = value;
            return Result<void>();
        }
        catch (const std::exception& e) {
            return Result<void>("Memory write failed: " + std::string(e.what()));
        }
    }
    
    Result<void> writeInt(Address addr, uint32_t value) override {
        std::lock_guard<std::mutex> lock(mutex_);
        
        if (validator_) {
            auto validation = validator_->validateAddress(addr, sizeof(uint32_t));
            if (!validation) {
                return validation;
            }
        }
        
        try {
            ProtectionGuard guard(addr, sizeof(uint32_t), useVirtualProtect_);
            if (useVirtualProtect_ && !guard.isActive()) {
                return Result<void>("Failed to change memory protection");
            }
            
            *reinterpret_cast<uint32_t*>(addr) = value;
            return Result<void>();
        }
        catch (const std::exception& e) {
            return Result<void>("Memory write failed: " + std::string(e.what()));
        }
    }
    
    Result<void> writeDouble(Address addr, double value) override {
        std::lock_guard<std::mutex> lock(mutex_);
        
        if (validator_) {
            auto validation = validator_->validateAddress(addr, sizeof(double));
            if (!validation) {
                return validation;
            }
        }
        
        try {
            ProtectionGuard guard(addr, sizeof(double), useVirtualProtect_);
            if (useVirtualProtect_ && !guard.isActive()) {
                return Result<void>("Failed to change memory protection");
            }
            
            *reinterpret_cast<double*>(addr) = value;
            return Result<void>();
        }
        catch (const std::exception& e) {
            return Result<void>("Memory write failed: " + std::string(e.what()));
        }
    }
    
    Result<void> writeString(Address addr, const std::string& value) override {
        std::lock_guard<std::mutex> lock(mutex_);
        
        if (validator_) {
            auto validation = validator_->validateAddress(addr, value.length());
            if (!validation) {
                return validation;
            }
        }
        
        try {
            ProtectionGuard guard(addr, value.length(), useVirtualProtect_);
            if (useVirtualProtect_ && !guard.isActive()) {
                return Result<void>("Failed to change memory protection");
            }
            
            std::memcpy(reinterpret_cast<void*>(addr), value.c_str(), value.length());
            return Result<void>();
        }
        catch (const std::exception& e) {
            return Result<void>("Memory write failed: " + std::string(e.what()));
        }
    }
    
    Result<void> writeBytes(Address addr, const std::vector<uint8_t>& data) override {
        std::lock_guard<std::mutex> lock(mutex_);
        
        if (data.empty()) {
            return Result<void>("Empty data array");
        }
        
        if (validator_) {
            auto validation = validator_->validateAddress(addr, data.size());
            if (!validation) {
                return validation;
            }
        }
        
        try {
            if (useVirtualProtect_) {
                // 对于字节数组，逐个写入以避免大范围保护更改
                for (size_t i = 0; i < data.size(); ++i) {
                    auto result = writeByte(addr + i, data[i]);
                    if (!result) {
                        return result;
                    }
                }
            } else {
                for (size_t i = 0; i < data.size(); ++i) {
                    *reinterpret_cast<uint8_t*>(addr + i) = data[i];
                }
            }
            return Result<void>();
        }
        catch (const std::exception& e) {
            return Result<void>("Memory write failed: " + std::string(e.what()));
        }
    }
    
    Result<void> fillBytes(Address addr, uint8_t value, size_t count) override {
        std::lock_guard<std::mutex> lock(mutex_);
        
        if (validator_) {
            auto validation = validator_->validateAddress(addr, count);
            if (!validation) {
                return validation;
            }
        }
        
        try {
            ProtectionGuard guard(addr, count, useVirtualProtect_);
            if (useVirtualProtect_ && !guard.isActive()) {
                return Result<void>("Failed to change memory protection");
            }
            
            std::memset(reinterpret_cast<void*>(addr), value, count);
            return Result<void>();
        }
        catch (const std::exception& e) {
            return Result<void>("Memory fill failed: " + std::string(e.what()));
        }
    }
    
    Result<void> applyCodeCave(void* caveFunction, Address targetAddr, int nopCount) override {
        std::lock_guard<std::mutex> lock(mutex_);
        
        if (!caveFunction) {
            return Result<void>("Invalid cave function pointer");
        }
        
        if (validator_) {
            auto validation = validator_->validateExecutableAddress(targetAddr);
            if (!validation) {
                return validation;
            }
        }
        
        try {
            // 填充NOP指令
            if (nopCount > 0) {
                auto fillResult = fillBytes(targetAddr, 0x90, nopCount);
                if (!fillResult) {
                    return fillResult;
                }
            }
            
            // 写入跳转指令
            auto jumpResult = writeByte(targetAddr, 0xE9);
            if (!jumpResult) {
                return jumpResult;
            }
            
            // 计算相对地址
            int32_t relativeAddr = static_cast<int32_t>(
                reinterpret_cast<Address>(caveFunction) - targetAddr - 5
            );
            
            auto addrResult = writeInt(targetAddr + 1, static_cast<uint32_t>(relativeAddr));
            if (!addrResult) {
                return addrResult;
            }
            
            return Result<void>();
        }
        catch (const std::exception& e) {
            return Result<void>("Code cave application failed: " + std::string(e.what()));
        }
    }
    
    Result<void> setHook(bool attach, void** targetFunc, void* detourFunc) override {
        std::lock_guard<std::mutex> lock(mutex_);
        
        if (!targetFunc || !detourFunc) {
            return Result<void>("Invalid function pointers");
        }
        
        try {
            if (DetourTransactionBegin() != NO_ERROR) {
                return Result<void>("Failed to begin detour transaction");
            }
            
            HANDLE currentThread = GetCurrentThread();
            if (DetourUpdateThread(currentThread) != NO_ERROR) {
                DetourTransactionAbort();
                return Result<void>("Failed to update thread for detour");
            }
            
            auto detourOperation = attach ? DetourAttach : DetourDetach;
            if (detourOperation(targetFunc, detourFunc) != NO_ERROR) {
                DetourTransactionAbort();
                return Result<void>("Failed to attach/detach detour");
            }
            
            if (DetourTransactionCommit() != NO_ERROR) {
                DetourTransactionAbort();
                return Result<void>("Failed to commit detour transaction");
            }
            
            return Result<void>();
        }
        catch (const std::exception& e) {
            DetourTransactionAbort();
            return Result<void>("Hook operation failed: " + std::string(e.what()));
        }
    }
    
    Result<ProtectionFlags> changeProtection(Address addr, size_t size, ProtectionFlags newProtection) override {
        DWORD windowsProtection = PAGE_NOACCESS;
        
        // 转换保护标志
        if (newProtection & Protection::READ_WRITE_EXECUTE) {
            windowsProtection = PAGE_EXECUTE_READWRITE;
        } else if (newProtection & Protection::READ_EXECUTE) {
            windowsProtection = PAGE_EXECUTE_READ;
        } else if (newProtection & Protection::READ_WRITE) {
            windowsProtection = PAGE_READWRITE;
        } else if (newProtection & Protection::READ) {
            windowsProtection = PAGE_READONLY;
        } else if (newProtection & Protection::EXECUTE) {
            windowsProtection = PAGE_EXECUTE;
        }
        
        DWORD oldProtection;
        if (!VirtualProtect(reinterpret_cast<LPVOID>(addr), size, windowsProtection, &oldProtection)) {
            return Result<ProtectionFlags>("Failed to change memory protection");
        }
        
        // 转换回我们的标志格式
        ProtectionFlags oldFlags = 0;
        if (oldProtection & (PAGE_READONLY | PAGE_READWRITE | PAGE_EXECUTE_READ | PAGE_EXECUTE_READWRITE)) {
            oldFlags |= Protection::READ;
        }
        if (oldProtection & (PAGE_READWRITE | PAGE_EXECUTE_READWRITE)) {
            oldFlags |= Protection::WRITE;
        }
        if (oldProtection & (PAGE_EXECUTE | PAGE_EXECUTE_READ | PAGE_EXECUTE_READWRITE)) {
            oldFlags |= Protection::EXECUTE;
        }
        
        return Result<ProtectionFlags>(oldFlags);
    }
    
    Result<void> restoreProtection(Address addr, size_t size, ProtectionFlags oldProtection) override {
        auto result = changeProtection(addr, size, oldProtection);
        if (!result) {
            return Result<void>(result.getError());
        }
        return Result<void>();
    }
};

// 地址管理器实现
class WindowsAddressManager : public IAddressManager {
private:
    struct AddressInfo {
        Address address;
        size_t size;
        std::string description;
    };

    std::shared_ptr<IAddressValidator> validator_;
    std::unordered_map<std::string, AddressInfo> addresses_;
    mutable std::mutex mutex_;

public:
    WindowsAddressManager(std::shared_ptr<IAddressValidator> validator)
        : validator_(validator) {}

    Result<void> registerAddress(const std::string& name, Address addr, size_t size) override {
        std::lock_guard<std::mutex> lock(mutex_);

        if (name.empty()) {
            return Result<void>("Address name cannot be empty");
        }

        if (validator_) {
            auto validation = validator_->validateAddress(addr, size);
            if (!validation) {
                return Result<void>("Invalid address for '" + name + "': " + validation.getError());
            }
        }

        addresses_[name] = {addr, size, ""};
        return Result<void>();
    }

    Result<Address> getAddress(const std::string& name) const override {
        std::lock_guard<std::mutex> lock(mutex_);

        auto it = addresses_.find(name);
        if (it == addresses_.end()) {
            return Result<Address>("Address '" + name + "' not found");
        }

        return Result<Address>(it->second.address);
    }

    Result<void> validateRegisteredAddress(const std::string& name) const override {
        std::lock_guard<std::mutex> lock(mutex_);

        auto it = addresses_.find(name);
        if (it == addresses_.end()) {
            return Result<void>("Address '" + name + "' not registered");
        }

        if (validator_) {
            return validator_->validateAddress(it->second.address, it->second.size);
        }

        return Result<void>();
    }

    Result<void> validateAddressRange(Address addr, size_t size) const override {
        if (validator_) {
            return validator_->validateAddress(addr, size);
        }
        return Result<void>();
    }

    bool isValidExecutableAddress(Address addr) const override {
        if (validator_) {
            auto result = validator_->validateExecutableAddress(addr);
            return result.isSuccess();
        }
        return true; // 如果没有验证器，假设有效
    }

    std::vector<std::string> getRegisteredAddressNames() const override {
        std::lock_guard<std::mutex> lock(mutex_);

        std::vector<std::string> names;
        names.reserve(addresses_.size());

        for (const auto& pair : addresses_) {
            names.push_back(pair.first);
        }

        return names;
    }

    size_t getRegisteredAddressCount() const override {
        std::lock_guard<std::mutex> lock(mutex_);
        return addresses_.size();
    }
};

// 全局实例
static std::unique_ptr<IMemoryPatcher> g_memoryPatcher;
static std::unique_ptr<IAddressManager> g_addressManager;
static std::mutex g_initMutex;

// 工厂方法实现
std::unique_ptr<IAddressValidator> MemoryManagerFactory::createAddressValidator() {
    return std::make_unique<WindowsAddressValidator>();
}

std::unique_ptr<IMemoryPatcher> MemoryManagerFactory::createMemoryPatcher(
    std::shared_ptr<IAddressValidator> validator, bool useVirtualProtect) {
    return std::make_unique<WindowsMemoryPatcher>(validator, useVirtualProtect);
}

std::unique_ptr<IAddressManager> MemoryManagerFactory::createAddressManager(
    std::shared_ptr<IAddressValidator> validator) {
    return std::make_unique<WindowsAddressManager>(validator);
}

// 全局访问接口实现
IMemoryPatcher& getMemoryPatcher() {
    if (!g_memoryPatcher) {
        throw std::runtime_error("Memory manager not initialized. Call initializeMemoryManager() first.");
    }
    return *g_memoryPatcher;
}

IAddressManager& getAddressManager() {
    if (!g_addressManager) {
        throw std::runtime_error("Memory manager not initialized. Call initializeMemoryManager() first.");
    }
    return *g_addressManager;
}

// 初始化和清理
Result<void> initializeMemoryManager(bool useVirtualProtect) {
    std::lock_guard<std::mutex> lock(g_initMutex);

    try {
        if (g_memoryPatcher || g_addressManager) {
            return Result<void>("Memory manager already initialized");
        }

        auto validator = MemoryManagerFactory::createAddressValidator();
        auto sharedValidator = std::shared_ptr<IAddressValidator>(validator.release());

        g_memoryPatcher = MemoryManagerFactory::createMemoryPatcher(sharedValidator, useVirtualProtect);
        g_addressManager = MemoryManagerFactory::createAddressManager(sharedValidator);

        std::cout << "[MemoryManager] Initialized successfully" << std::endl;
        return Result<void>();
    }
    catch (const std::exception& e) {
        return Result<void>("Failed to initialize memory manager: " + std::string(e.what()));
    }
}

void shutdownMemoryManager() {
    std::lock_guard<std::mutex> lock(g_initMutex);

    g_memoryPatcher.reset();
    g_addressManager.reset();

    std::cout << "[MemoryManager] Shutdown completed" << std::endl;
}

} // namespace EzorsiaMemory
