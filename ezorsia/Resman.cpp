#include "stdafx.h"
#include "Resman.h"
#include "Memory.h"
#include <algorithm>
#include <iostream>

<PERSON><PERSON>* Resman::instance = nullptr;

// global variables
namespace {
    VARIANTARG errorVar = {VT_ERROR, 0, 0, 0x80020004};
    VARIANTARG pvarg = {VT_I4, 0, 0, 0};
    
    // function pointer definitions
    typedef int(__fastcall* _bstr_constructor)(void* ecx, void* edx, const char* str);
    typedef int(__fastcall* _bstr_constructor_wchar)(void* ecx, void* edx, const wchar_t* str);
    typedef int(__fastcall* _bstr_release)(void* ecx, void* edx);

    typedef DWORD(__fastcall* _Ztl_variant_t__GetUnknown)(VARIANTARG* This, void* notuse, bool a2, bool a3);
    typedef DWORD(__fastcall* _IWzCanvas__GetProperty)(DWORD* This, void* notuse, IUnknown** a2);
    typedef VARIANTARG*(__fastcall* _IWzProperty__GetItem)(IUnknown* This, void* notuse, VARIANTARG* pvargDest, int* sPath);
    typedef int(__fastcall* _IWzUOL__GetfilePath)(DWORD* This, void* notuse, int a2);
    typedef IUnknown*(__fastcall* _IWzUOL_QueryInterface)(DWORD* This, void* notuse, IUnknown* a2);
    typedef VARIANTARG*(__fastcall* _IWzResMan__GetObjectA)(DWORD* This, void* notuse, VARIANTARG* pvargDest, int* sUOL, int vParam, int vAux);
    typedef DWORD*(__cdecl* _get_unknown)(DWORD*, VARIANT*);
    typedef int(__fastcall* _IWzCanvas_operator_equal)(DWORD* This, void* notuse, DWORD* a2);
    typedef int(__fastcall* _IWzCanvas__Getwidth)(DWORD* This, void* notuse);
    typedef int(__fastcall* _IWzCanvas__Getheight)(DWORD* This, void* notuse);

    // function pointer instances
    _bstr_constructor bstr_constructor = (_bstr_constructor)0x406301;
    _bstr_constructor_wchar bstr_constructor_wchar = (_bstr_constructor_wchar)0x402BE8;
    _bstr_release bstr_release = (_bstr_release)0x402EA5;

    _Ztl_variant_t__GetUnknown Ztl_variant_t__GetUnknown = (_Ztl_variant_t__GetUnknown)0x4032B2;
    _IWzCanvas__GetProperty IWzCanvas__GetProperty = (_IWzCanvas__GetProperty)0x404AD7;
    _IWzProperty__GetItem IWzProperty__GetItem = (_IWzProperty__GetItem)0x403935;
    _IWzUOL__GetfilePath IWzUOL__GetfilePath = (_IWzUOL__GetfilePath)0x414C70;
    _IWzUOL_QueryInterface IWzUOL_QueryInterface = (_IWzUOL_QueryInterface)0x416838;
    _IWzResMan__GetObjectA IWzResMan__GetObjectA = (_IWzResMan__GetObjectA)0x403A93;
    _get_unknown get_unknown = (_get_unknown)0x414ADA;
    _IWzCanvas_operator_equal IWzCanvas_operator_equal = (_IWzCanvas_operator_equal)0x41E42B;
    _IWzCanvas__Getwidth IWzCanvas__Getwidth = (_IWzCanvas__Getwidth)0x40B920;
    _IWzCanvas__Getheight IWzCanvas__Getheight = (_IWzCanvas__Getheight)0x40B947;

    // global pointers
    void** g_rm = (void**)0xBF14E8;
}

void Resman::initialize() {
    instance = this;
    Memory::SetHook(true, reinterpret_cast<void**>(&IWzProperty__GetItem), IWzProperty__GetItem_Hook);
    Memory::SetHook(true, reinterpret_cast<void**>(&IWzResMan__GetObjectA), IWzResMan__GetObjectA_Hook);
    Memory::SetHook(true, reinterpret_cast<void**>(&IWzCanvas_operator_equal), IWzCanvas_operator_equal_Hook);
}

void Resman::cleanup() {
    imgPathMap.clear();
    instance = nullptr;
}

DWORD* Resman::GetResManInstance() {
    return (DWORD*)*g_rm;
}

void* Resman::getUOLProperty(VARIANT* prop, void** result) {
    if (prop == NULL || result == NULL)
        return NULL;
    IUnknown* pUnk = (IUnknown*)Ztl_variant_t__GetUnknown(prop, nullptr, 0, 0);
    if (pUnk) {
        pUnk->AddRef();
        IUnknown* pWzUOL = NULL;
        void* retValue = NULL;

        IWzUOL_QueryInterface((DWORD*)&pWzUOL, nullptr, (IUnknown*)&pUnk);

        if (pWzUOL) {
            IWzUOL__GetfilePath((DWORD*)pWzUOL, nullptr, (int)result);
            if (*result)
                retValue = *result;
            pWzUOL->Release();
        }
        pUnk->Release();
        return retValue;
    }
    return NULL;
}

std::wstring Resman::getImgFullPath(std::wstring strT) {
    std::wstring lstr = strT;
    std::transform(lstr.begin(), lstr.end(), lstr.begin(), towlower);

    int pos = lstr.rfind(L".img");
    if (pos != std::string::npos) {
        pos += 4;
        strT = strT.substr(0, pos);
        strT += L"/";
    }
    return strT;
}

DWORD Resman::getCanvasPropertyByPath(std::wstring path, DWORD* result) {
    VARIANT varDest = {0};
    VARIANT var1 = {0};
    VARIANT var2 = {0};
    DWORD varUnk = 0;
    void* sUol = NULL;
    bstr_constructor_wchar(&sUol, nullptr, path.c_str());
    auto v9 = IWzResMan__GetObjectA(GetResManInstance(), nullptr, &varDest, (int*)sUol, (int)&var1, (int)&var2);
    auto v10 = get_unknown(&varUnk, v9);
    return IWzCanvas_operator_equal(result, nullptr, v10);
}

// Hook function implementation
int __fastcall Resman::IWzCanvas_operator_equal_Hook(DWORD* This, void* notuse, DWORD* a2) {
    if (!instance) return IWzCanvas_operator_equal(This, nullptr, a2);
    

    auto ret = IWzCanvas_operator_equal(This, nullptr, a2);
    
    // check pointer validity before use
    if (!This || !*This) return ret;

    int w = IWzCanvas__Getwidth((DWORD*)*This, nullptr);
    int h = IWzCanvas__Getheight((DWORD*)*This, nullptr);

    if (w > 1 || h > 1) return ret;

    // declare all resources here
    IUnknown* prop = NULL;
    void* pStrInlink = NULL;
    void* pStrOutlink = NULL;
    VARIANT dst = {0};
    
    // get property
    IWzCanvas__GetProperty((DWORD*)*This, nullptr, &prop);
    if (!prop) return ret;  // safe here, no other resources allocated yet

    bstr_constructor(&pStrInlink, nullptr, "_inlink");
    if (pStrInlink) {
        IWzProperty__GetItem(prop, nullptr, &dst, (int*)pStrInlink);
        if (dst.vt == VT_BSTR && dst.bstrVal) {
            IUnknown* pUnk = (IUnknown*)*a2;
            if (instance->imgPathMap.find(pUnk) != instance->imgPathMap.end()) {
                // std::cout << "_inlink: " << dst.bstrVal << ", FullPath: " << instance->imgPathMap[pUnk]->c_str() << std::endl;
                DWORD ptr = 0;
                ret = instance->getCanvasPropertyByPath(
                    instance->getImgFullPath(instance->imgPathMap[pUnk]->c_str()) + dst.bstrVal, 
                    (DWORD*)&ptr
                );
                if (ptr) *This = ptr;
            }
        }
    }

    // process outlink
    bstr_constructor(&pStrOutlink, nullptr, "_outlink");
    if (pStrOutlink) {
        IWzProperty__GetItem(prop, nullptr, &dst, (int*)pStrOutlink);
        if (dst.vt == VT_BSTR && dst.bstrVal) {
            // std::cout << "_outlink: " << dst.bstrVal << std::endl;
            DWORD ptr = 0;
            ret = instance->getCanvasPropertyByPath(dst.bstrVal, (DWORD*)&ptr);
            if (ptr) *This = ptr;
        }
    }

    // clean up all resources
    if (pStrInlink) bstr_release(pStrInlink, nullptr);
    if (pStrOutlink) bstr_release(pStrOutlink, nullptr);
    if (dst.vt != VT_EMPTY) VariantClear(&dst);
    if (prop) prop->Release();

    return ret;
}

VARIANTARG* __fastcall Resman::IWzResMan__GetObjectA_Hook(DWORD* This, void* notuse, 
    VARIANTARG* pvargDest, int* sUOL, int vParam, int vAux) {
    if (!instance) return IWzResMan__GetObjectA(This, nullptr, pvargDest, sUOL, vParam, vAux);

    std::wstring strT = (wchar_t*)*sUOL;
    auto ret = IWzResMan__GetObjectA(This, nullptr, pvargDest, sUOL, vParam, vAux);
    if (ret && ret->vt == VT_UNKNOWN) {
        instance->imgPathMap[ret->punkVal] = std::make_shared<std::wstring>(strT);
    }
    return ret;
}

VARIANTARG* __fastcall Resman::IWzProperty__GetItem_Hook(IUnknown* This, void* notuse, 
    VARIANTARG* pvargDest, int* sPath) {
    if (!instance) return IWzProperty__GetItem(This, nullptr, pvargDest, sPath);

    std::wstring strT = (wchar_t*)*sPath;
    auto ret = IWzProperty__GetItem(This, nullptr, pvargDest, sPath);
    
    if (pvargDest->vt == VT_UNKNOWN) {
        if (instance->imgPathMap.find(This) != instance->imgPathMap.end()) {
            instance->imgPathMap[pvargDest->punkVal] = instance->imgPathMap[This];
        }
    }
    
    void* sUOL = NULL;
    instance->getUOLProperty(pvargDest, &sUOL);
    if (sUOL) {
        VARIANTARG pvarg1 = errorVar;
        VARIANTARG pvarg2 = errorVar;
        ret = IWzResMan__GetObjectA(instance->GetResManInstance(), nullptr, pvargDest, 
            (int*)sUOL, (int)&pvarg1, (int)&pvarg2);
        
        VariantClear(&pvarg1);
        VariantClear(&pvarg2);
        bstr_release(sUOL, nullptr);
    }
    return ret;
}


VARIANTARG* getGetObjectAForPath(DWORD* This, std::wstring path, VARIANTARG* ret) {
	try {
		VARIANTARG pvarg1 = errorVar;
		VARIANTARG pvarg2 = errorVar;
		IWzResMan__GetObjectA(This, nullptr, ret, (int*)&path, (int)&pvarg1, (int)&pvarg2);
		return ret;
	}
	catch (...) {
	}
	return nullptr;
}

VARIANTARG* getGetObjectAForPath(DWORD* This, std::wstring path) {
	try {
		VARIANTARG ret = {};
		return getGetObjectAForPath(This, path, &ret);
	}
	catch (...) {
	}
	return nullptr;
}

VARIANTARG* getGetObjectAForPath(std::wstring path) {
	return getGetObjectAForPath(Resman::GetResManInstance(), path);
}

IWzProperty* getIWzPropertyForPath(DWORD* This, std::wstring path) {
	try {
		auto ret = getGetObjectAForPath(This, path);
		if (ret && ret->vt == VT_UNKNOWN)
		{
			return (IWzProperty*)ret->punkVal;
		}
	}
	catch (...) {
	}
	return nullptr;
}

IWzProperty* getIWzPropertyForPath(std::wstring path) {
	return getIWzPropertyForPath(Resman::GetResManInstance(), path);
}
