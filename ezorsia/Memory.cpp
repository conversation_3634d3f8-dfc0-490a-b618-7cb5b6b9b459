#include "stdafx.h"
#include "Memory.h"
#include "MemoryAdapter.h"
#include "detours.h"
//#pragma optimize("", off) //non-optimized function for testing purposes

// 注意：Memory类已被重构为使用新的内存管理系统
// UseVirtuProtect现在通过MemoryAdapter进行管理
bool Memory::UseVirtuProtect = true;

bool Memory::SetHook(bool attach, void** ptrTarget, void* ptrDetour)
{
    // 重构：使用新的内存管理系统
    try {
        // 同步UseVirtuProtect设置到MemoryAdapter
        EzorsiaMemory::MemoryAdapter::setUseVirtualProtect(UseVirtuProtect);
        return EzorsiaMemory::MemoryAdapter::SetHook(attach, ptrTarget, ptrDetour);
    }
    catch (const std::exception& e) {
        // 如果新系统失败，回退到原始实现
        if (DetourTransactionBegin() != NO_ERROR)
        {
            return false;
        }

        HANDLE pCurThread = GetCurrentThread();

        if (DetourUpdateThread(pCurThread) == NO_ERROR)
        {
            auto pDetourFunc = attach ? DetourAttach : DetourDetach;

            if (pDetourFunc(ptrTarget, ptrDetour) == NO_ERROR)
            {
                if (DetourTransactionCommit() == NO_ERROR)
                {
                    return true;
                }
            }
        }

        DetourTransactionAbort();
        return false;
    }
}

void Memory::FillBytes(const DWORD dwOriginAddress, const unsigned char ucValue, const int nCount) {
    // 重构：使用新的内存管理系统
    try {
        EzorsiaMemory::MemoryAdapter::setUseVirtualProtect(UseVirtuProtect);
        EzorsiaMemory::MemoryAdapter::FillBytes(dwOriginAddress, ucValue, nCount);
    }
    catch (const std::exception& e) {
        // 如果新系统失败，回退到原始实现
        if (UseVirtuProtect) {
            DWORD dwOldProtect;
            VirtualProtect((LPVOID)dwOriginAddress, nCount, PAGE_EXECUTE_READWRITE, &dwOldProtect);
            memset((void*)dwOriginAddress, ucValue, nCount);
            VirtualProtect((LPVOID)dwOriginAddress, nCount, dwOldProtect, &dwOldProtect);
        }
        else { memset((void*)dwOriginAddress, ucValue, nCount); }
    }
}

void Memory::WriteString(const DWORD dwOriginAddress, const char* sContent) {
    // 重构：使用新的内存管理系统
    try {
        EzorsiaMemory::MemoryAdapter::setUseVirtualProtect(UseVirtuProtect);
        EzorsiaMemory::MemoryAdapter::WriteString(dwOriginAddress, sContent);
    }
    catch (const std::exception& e) {
        // 如果新系统失败，回退到原始实现
        const size_t nSize = strlen(sContent);
        if (UseVirtuProtect) {
            DWORD dwOldProtect;
            VirtualProtect((LPVOID)dwOriginAddress, nSize, PAGE_EXECUTE_READWRITE, &dwOldProtect);
            memcpy((void*)dwOriginAddress, sContent, nSize);
            VirtualProtect((LPVOID)dwOriginAddress, nSize, dwOldProtect, &dwOldProtect);
        }
        else { memcpy((void*)dwOriginAddress, sContent, nSize); }
    }
}

void Memory::WriteByte(const DWORD dwOriginAddress, const unsigned char ucValue) {
    // 重构：使用新的内存管理系统
    try {
        EzorsiaMemory::MemoryAdapter::setUseVirtualProtect(UseVirtuProtect);
        EzorsiaMemory::MemoryAdapter::WriteByte(dwOriginAddress, ucValue);
    }
    catch (const std::exception& e) {
        // 如果新系统失败，回退到原始实现
        if (UseVirtuProtect) {
            DWORD dwOldProtect;
            VirtualProtect((LPVOID)dwOriginAddress, sizeof(unsigned char), PAGE_EXECUTE_READWRITE, &dwOldProtect);
            *(unsigned char*)dwOriginAddress = ucValue;
            VirtualProtect((LPVOID)dwOriginAddress, sizeof(unsigned char), dwOldProtect, &dwOldProtect);
        }
        else { *(unsigned char*)dwOriginAddress = ucValue; }
    }
}

void Memory::WriteShort(const DWORD dwOriginAddress, const unsigned short usValue) {
    // 重构：使用新的内存管理系统
    try {
        EzorsiaMemory::MemoryAdapter::setUseVirtualProtect(UseVirtuProtect);
        EzorsiaMemory::MemoryAdapter::WriteShort(dwOriginAddress, usValue);
    }
    catch (const std::exception& e) {
        // 如果新系统失败，回退到原始实现
        if (UseVirtuProtect) {
            DWORD dwOldProtect;
            VirtualProtect((LPVOID)dwOriginAddress, sizeof(unsigned short), PAGE_EXECUTE_READWRITE, &dwOldProtect);
            *(unsigned short*)dwOriginAddress = usValue;
            VirtualProtect((LPVOID)dwOriginAddress, sizeof(unsigned short), dwOldProtect, &dwOldProtect);
        }
        else { *(unsigned short*)dwOriginAddress = usValue; }
    }
}

void Memory::WriteInt(const DWORD dwOriginAddress, const unsigned int dwValue) {
    // 重构：使用新的内存管理系统
    try {
        EzorsiaMemory::MemoryAdapter::setUseVirtualProtect(UseVirtuProtect);
        EzorsiaMemory::MemoryAdapter::WriteInt(dwOriginAddress, dwValue);
    }
    catch (const std::exception& e) {
        // 如果新系统失败，回退到原始实现
        if (UseVirtuProtect) {
            DWORD dwOldProtect;
            VirtualProtect((LPVOID)dwOriginAddress, sizeof(unsigned int), PAGE_EXECUTE_READWRITE, &dwOldProtect);
            *(unsigned int*)dwOriginAddress = dwValue;
            VirtualProtect((LPVOID)dwOriginAddress, sizeof(unsigned int), dwOldProtect, &dwOldProtect);
        }
        else { *(unsigned int*)dwOriginAddress = dwValue; }
    }
}

void Memory::WriteDouble(const DWORD dwOriginAddress, const double dwValue) {
    // 重构：使用新的内存管理系统
    try {
        EzorsiaMemory::MemoryAdapter::setUseVirtualProtect(UseVirtuProtect);
        EzorsiaMemory::MemoryAdapter::WriteDouble(dwOriginAddress, dwValue);
    }
    catch (const std::exception& e) {
        // 如果新系统失败，回退到原始实现
        if (UseVirtuProtect) {
            DWORD dwOldProtect;
            VirtualProtect((LPVOID)dwOriginAddress, sizeof(double), PAGE_EXECUTE_READWRITE, &dwOldProtect);
            *(double*)dwOriginAddress = dwValue;
            VirtualProtect((LPVOID)dwOriginAddress, sizeof(double), dwOldProtect, &dwOldProtect);
        }
        else { *(double*)dwOriginAddress = dwValue; }
    }
}

void Memory::WriteByteArray(const DWORD dwOriginAddress, unsigned char* ucValue, const int ucValueSize) {
    // 重构：使用新的内存管理系统
    try {
        EzorsiaMemory::MemoryAdapter::setUseVirtualProtect(UseVirtuProtect);
        EzorsiaMemory::MemoryAdapter::WriteByteArray(dwOriginAddress, ucValue, ucValueSize);
    }
    catch (const std::exception& e) {
        // 如果新系统失败，回退到原始实现
        if (UseVirtuProtect) {
            for (int i = 0; i < ucValueSize; i++) {
                const DWORD newAddr = dwOriginAddress + i;
                DWORD dwOldProtect;
                VirtualProtect((LPVOID)newAddr, sizeof(unsigned char), PAGE_EXECUTE_READWRITE, &dwOldProtect);
                *(unsigned char*)newAddr = ucValue[i];
                VirtualProtect((LPVOID)newAddr, sizeof(unsigned char), dwOldProtect, &dwOldProtect);
            }
        }
        else {
            for (int i = 0; i < ucValueSize; i++) {
                const DWORD newAddr = dwOriginAddress + i;
                *(unsigned char*)newAddr = ucValue[i];
            }
        }
    }
}

void Memory::CodeCave(void* ptrCodeCave, const DWORD dwOriginAddress, const int nNOPCount) { //tested and working
    // 重构：使用新的内存管理系统
    try {
        EzorsiaMemory::MemoryAdapter::setUseVirtualProtect(UseVirtuProtect);
        EzorsiaMemory::MemoryAdapter::CodeCave(ptrCodeCave, dwOriginAddress, nNOPCount);
    }
    catch (const std::exception& e) {
        // 如果新系统失败，回退到原始实现
        __try {
            if (nNOPCount) FillBytes(dwOriginAddress, 0x90, nNOPCount); // create space for the jmp
            WriteByte(dwOriginAddress, 0xe9); // jmp instruction
            WriteInt(dwOriginAddress + 1, (int)(((int)ptrCodeCave - (int)dwOriginAddress) - 5)); // [jmp(1 byte)][address(4 bytes)]
        } __except (EXCEPTION_EXECUTE_HANDLER) {}
    }
}
//#pragma optimize("", on)