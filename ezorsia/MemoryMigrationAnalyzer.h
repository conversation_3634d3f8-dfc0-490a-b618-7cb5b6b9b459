#pragma once
#include <string>
#include <vector>
#include <map>
#include <regex>

namespace EzorsiaMemory {

// 内存调用分析结果
struct MemoryCallInfo {
    std::string fileName;
    int lineNumber;
    std::string originalCall;
    std::string suggestedReplacement;
    std::string safeReplacement;
    bool requiresErrorHandling;
};

// 迁移统计信息
struct MigrationStats {
    int totalCalls = 0;
    int writeByteCalls = 0;
    int writeIntCalls = 0;
    int writeStringCalls = 0;
    int fillBytesCalls = 0;
    int setHookCalls = 0;
    int codeCaveCalls = 0;
    int writeByteArrayCalls = 0;
    int writeShortCalls = 0;
    int writeDoubleCalls = 0;
    
    std::map<std::string, int> fileCallCounts;
};

// 内存迁移分析器
class MemoryMigrationAnalyzer {
public:
    // 分析单个文件中的Memory调用
    static std::vector<MemoryCallInfo> analyzeFile(const std::string& filePath);
    
    // 分析整个项目中的Memory调用
    static std::vector<MemoryCallInfo> analyzeProject(const std::string& projectPath);
    
    // 生成迁移报告
    static std::string generateMigrationReport(const std::vector<MemoryCallInfo>& calls);
    
    // 生成统计信息
    static MigrationStats generateStats(const std::vector<MemoryCallInfo>& calls);
    
    // 生成迁移脚本
    static std::string generateMigrationScript(const std::vector<MemoryCallInfo>& calls);
    
    // 验证迁移后的代码
    static bool validateMigration(const std::string& filePath);
    
private:
    // 内存方法模式
    static const std::map<std::string, std::string> memoryMethodPatterns_;
    
    // 解析Memory调用
    static MemoryCallInfo parseMemoryCall(const std::string& line, int lineNumber, 
                                         const std::string& fileName);
    
    // 生成替换建议
    static std::string generateReplacement(const std::string& methodName, 
                                         const std::string& parameters, bool safe = false);
    
    // 检查是否需要错误处理
    static bool needsErrorHandling(const std::string& methodName);
    
    // 读取文件内容
    static std::string readFile(const std::string& filePath);
    
    // 获取项目中的所有源文件
    static std::vector<std::string> getSourceFiles(const std::string& projectPath);
};

// 迁移助手类
class MemoryMigrationHelper {
public:
    // 运行完整的迁移分析
    static void runMigrationAnalysis(const std::string& projectPath = ".");
    
    // 生成迁移报告文件
    static void generateReportFile(const std::string& outputPath = "migration_report.md");
    
    // 打印迁移统计
    static void printMigrationStats(const MigrationStats& stats);
    
    // 验证项目迁移状态
    static bool validateProjectMigration(const std::string& projectPath = ".");
    
private:
    static std::vector<MemoryCallInfo> lastAnalysisResults_;
    static MigrationStats lastStats_;
};

} // namespace EzorsiaMemory
