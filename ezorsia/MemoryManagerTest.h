#pragma once
#include "MemoryManager.h"
#include "MemoryAdapter.h"
#include "AddressRegistry.h"

namespace EzorsiaMemory {

// 内存管理器测试类
class MemoryManagerTest {
public:
    // 运行所有测试
    static bool runAllTests();
    
    // 基础功能测试
    static bool testBasicMemoryOperations();
    static bool testAddressValidation();
    static bool testAddressRegistry();
    static bool testMemoryAdapter();
    
    // 错误处理测试
    static bool testErrorHandling();
    static bool testInvalidAddresses();
    
    // 性能测试
    static bool testPerformance();
    
    // 兼容性测试
    static bool testBackwardCompatibility();
    
private:
    // 辅助方法
    static void logTestResult(const std::string& testName, bool passed);
    static bool allocateTestMemory(void** ptr, size_t size);
    static void freeTestMemory(void* ptr);
    static void printTestSummary(int passed, int total);
};

} // namespace EzorsiaMemory
