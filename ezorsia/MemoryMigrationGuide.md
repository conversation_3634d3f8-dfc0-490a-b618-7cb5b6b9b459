# Memory类迁移指南

## 概述

Memory类已被重构为使用新的内存管理系统。虽然原有的Memory类仍然可用，但建议迁移到新的EzorsiaMemory::MemoryAdapter以获得更好的错误处理和类型安全性。

## 迁移策略

### 阶段1：兼容性层（当前状态）
- Memory类现在内部使用MemoryAdapter
- 保持所有原有接口不变
- 添加deprecated标记提醒开发者

### 阶段2：渐进式迁移
- 逐步将Memory::调用替换为MemoryAdapter::调用
- 使用宏定义支持平滑过渡
- 保持向后兼容性

### 阶段3：完全迁移
- 移除Memory类（可选）
- 全面使用新的内存管理系统

## 迁移对照表

### 基础内存操作

| 原有方法 | 新方法 | 安全版本 |
|---------|--------|----------|
| `Memory::WriteByte(addr, value)` | `MemoryAdapter::WriteByte(addr, value)` | `MemoryAdapter::SafeWriteByte(addr, value)` |
| `Memory::WriteInt(addr, value)` | `MemoryAdapter::WriteInt(addr, value)` | `MemoryAdapter::SafeWriteInt(addr, value)` |
| `Memory::WriteString(addr, str)` | `MemoryAdapter::WriteString(addr, str)` | `MemoryAdapter::SafeWriteString(addr, str)` |
| `Memory::FillBytes(addr, val, count)` | `MemoryAdapter::FillBytes(addr, val, count)` | `MemoryAdapter::SafeFillBytes(addr, val, count)` |

### 高级操作

| 原有方法 | 新方法 | 安全版本 |
|---------|--------|----------|
| `Memory::SetHook(attach, target, detour)` | `MemoryAdapter::SetHook(attach, target, detour)` | `MemoryAdapter::SafeSetHook(attach, target, detour)` |
| `Memory::CodeCave(cave, addr, nops)` | `MemoryAdapter::CodeCave(cave, addr, nops)` | `MemoryAdapter::SafeCodeCave(cave, addr, nops)` |

## 使用宏定义进行迁移

### 启用安全模式
```cpp
#define MEMORY_SAFE_MODE 1
#include "MemoryAdapter.h"

// 现在所有Memory_XXX宏都会使用新的内存管理器
Memory_WriteByte(0x00400000, 0x90);
Memory_WriteInt(0x00400004, 0x12345678);
```

### 手动迁移示例

#### 原有代码
```cpp
Memory::WriteByte(0x00400000, 0x90);
Memory::WriteInt(0x00400004, 0x12345678);
Memory::FillBytes(0x00400008, 0x00, 16);
```

#### 迁移后（兼容版本）
```cpp
using namespace EzorsiaMemory;
MemoryAdapter::WriteByte(0x00400000, 0x90);
MemoryAdapter::WriteInt(0x00400004, 0x12345678);
MemoryAdapter::FillBytes(0x00400008, 0x00, 16);
```

#### 迁移后（安全版本）
```cpp
using namespace EzorsiaMemory;

auto result1 = MemoryAdapter::SafeWriteByte(0x00400000, 0x90);
if (!result1) {
    std::cerr << "WriteByte failed: " << result1.getError() << std::endl;
}

auto result2 = MemoryAdapter::SafeWriteInt(0x00400004, 0x12345678);
if (!result2) {
    std::cerr << "WriteInt failed: " << result2.getError() << std::endl;
}

auto result3 = MemoryAdapter::SafeFillBytes(0x00400008, 0x00, 16);
if (!result3) {
    std::cerr << "FillBytes failed: " << result3.getError() << std::endl;
}
```

## 新功能优势

### 1. 错误处理
- 安全版本返回Result<T>类型
- 详细的错误信息
- 不会静默失败

### 2. 地址验证
- 自动验证内存地址有效性
- 防止访问无效内存区域
- 支持地址范围检查

### 3. 类型安全
- 强类型地址系统
- 编译时类型检查
- 减少类型转换错误

### 4. 线程安全
- 内置互斥锁保护
- 支持多线程环境
- 避免竞态条件

### 5. RAII内存保护
- 自动管理VirtualProtect
- 异常安全
- 防止内存保护泄漏

## 配置选项

### UseVirtualProtect同步
```cpp
// Memory类的UseVirtuProtect会自动同步到MemoryAdapter
Memory::UseVirtuProtect = false;
// 这会影响MemoryAdapter的行为

// 也可以直接设置MemoryAdapter
MemoryAdapter::setUseVirtualProtect(true);
```

### 错误处理配置
```cpp
// 设置自定义错误处理器
MemoryAdapter::setErrorHandler([](const std::string& error) {
    std::cerr << "Memory Error: " << error << std::endl;
});
```

## 迁移检查清单

- [ ] 识别所有Memory::调用
- [ ] 评估是否需要错误处理
- [ ] 选择兼容版本或安全版本
- [ ] 更新包含头文件
- [ ] 测试迁移后的功能
- [ ] 验证错误处理逻辑
- [ ] 更新相关文档

## 常见问题

### Q: 迁移后性能会受影响吗？
A: 新系统增加了地址验证和错误处理，会有轻微的性能开销，但提供了更好的安全性和可靠性。

### Q: 可以混合使用Memory和MemoryAdapter吗？
A: 可以，Memory类现在内部使用MemoryAdapter，两者可以安全混合使用。

### Q: 如何处理迁移过程中的编译错误？
A: 确保包含正确的头文件，使用正确的命名空间，检查方法签名是否匹配。

### Q: 新系统是否支持所有原有功能？
A: 是的，新系统完全支持所有原有功能，并提供了额外的安全特性。

## 技术支持

如果在迁移过程中遇到问题，请：
1. 检查编译错误和警告信息
2. 运行内存管理器测试套件
3. 查看详细的错误日志
4. 参考示例代码和文档
