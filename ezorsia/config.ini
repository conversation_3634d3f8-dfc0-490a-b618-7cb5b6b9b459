[general]
;what resolutions should the game use? (reccomended values: 1280x720, 1366x768; other values: 1600x900, 1920x1080, 1024x768)
width=1280
height=720

;what language should the game use? (default: language=EN)
language=CN

;what server IP address should the client try to connect to? (default: ServerIP_Address=127.0.0.1)
ServerIP_Address=127.0.0.1

ServerPort=8484

;custom number of gain messages (default 10; min value 6 which is the default value in vanilla, no more than 255 or you buffer overflow)
msgAmount=10

;Should the game start in window mode?	(true/false, default true)
windowedMode=true

;Should the Wizet and Nexon Logos be removed from game start?	(true/false, default true)
removeLogos=true

[optional]
;(Deprecated) sets the damage cap as well as cap of damage in stats window (default 199999.0, enter as #.0)
; damageCap=199999.0

;(Deprecated) sets use of tubi
; useTubi=false

;sets the cap for movement speed of characters (default 140, 32 bit integer)
speedMovementCap=140

;sets the cap for jump height of characters (default 123, 32 bit integer)
jumpCap=123

;sets the climb speed rate (default 1.0)
climbSpeed=1.0

;sets the auto climb speed mode (default false)
climbSpeedAuto=false

;only true if you directly edited the original frame in UI.wz and want to use that (true/false, default false)
CustomLoginFrame=false

;did you wz edit the cashshop backgrnd(s) to something else and are using it?(true/false, default false)
ownCashShopFrame=false

;is the server using a v62 exp table?(true/false, default false)
useV62_ExpTable=false

;Enable extra quickslots (true/false, default false)
ExtraQuickslots=false

;Enable memory optimization (true/false, default true)
MemoryOptimization=false
MemoryOptimizationDelay=0

;Enable inlink/outlink support (true/false, default true)
inOutLinkSupport=false

;Enable experimental feature (true/false, default false)
experimentalFeature=false

;rename up to 3 (default) dll names below with dll to be loaded from same folder (warning: conflicts with Ezorsia v2 will be overwritten)
;change the value back to the default value to turn off custom dll loading (default values CUSTOM.dll/CUSTOM2.dll/CUSTOM3.dll)
use_custom_dll_1=CUSTOM.dll
use_custom_dll_2=CUSTOM2.dll
use_custom_dll_3=CUSTOM3.dll

[debug]
;sleeps before loading custom dlls because they may not have themida bypass techniques built in
;adjust this number if the game is failing to launch (reccommended value range 30 to 160, default value 0)
sleepTime=0
showConsole=false