#pragma once
#include "MemoryManager.h"
#include "GameConstants.h"

namespace EzorsiaMemory {

// 地址注册器，用于将GameConstants中的地址注册到地址管理器
class AddressRegistry {
public:
    // 注册所有游戏常量地址
    static Result<void> registerAllGameAddresses();
    
    // 分类注册方法
    static Result<void> registerMemoryAddresses();
    static Result<void> registerUIAddresses();
    static Result<void> registerGameplayAddresses();
    static Result<void> registerNetworkAddresses();
    static Result<void> registerSystemAddresses();
    
    // 验证所有已注册地址
    static Result<void> validateAllRegisteredAddresses();
    
    // 获取注册统计信息
    static void printRegistrationStats();
    
private:
    // 辅助方法
    static Result<void> registerAddress(const std::string& name, uint32_t address, size_t size = 1, const std::string& description = "");
    static void logRegistration(const std::string& category, int successCount, int totalCount);
};

// 地址常量映射结构
struct AddressMapping {
    const char* name;
    uint32_t address;
    size_t size;
    const char* description;
};

// 地址映射表
namespace AddressMappings {
    // 内存相关地址
    extern const AddressMapping MEMORY_ADDRESSES[];
    extern const size_t MEMORY_ADDRESS_COUNT;
    
    // UI相关地址
    extern const AddressMapping UI_ADDRESSES[];
    extern const size_t UI_ADDRESS_COUNT;
    
    // 游戏玩法相关地址
    extern const AddressMapping GAMEPLAY_ADDRESSES[];
    extern const size_t GAMEPLAY_ADDRESS_COUNT;
    
    // 网络相关地址
    extern const AddressMapping NETWORK_ADDRESSES[];
    extern const size_t NETWORK_ADDRESS_COUNT;
    
    // 系统相关地址
    extern const AddressMapping SYSTEM_ADDRESSES[];
    extern const size_t SYSTEM_ADDRESS_COUNT;
}

} // namespace EzorsiaMemory
