#pragma once
#include <iostream>
#include <string>

namespace EzorsiaMemory {

// Memory类重构测试
class MemoryRefactorTest {
public:
    // 运行所有重构测试
    static bool runAllTests();
    
    // 测试Memory类兼容性
    static bool testMemoryCompatibility();
    
    // 测试Memory类与MemoryAdapter的一致性
    static bool testMemoryAdapterConsistency();
    
    // 测试UseVirtuProtect同步
    static bool testUseVirtuProtectSync();
    
    // 测试错误处理回退机制
    static bool testErrorHandlingFallback();
    
    // 测试deprecated警告
    static bool testDeprecatedWarnings();
    
private:
    // 分配测试内存
    static bool allocateTestMemory(void** memory, size_t size);
    
    // 释放测试内存
    static void freeTestMemory(void* memory);
    
    // 记录测试结果
    static void logTestResult(const std::string& testName, bool passed);
    
    // 打印测试摘要
    static void printTestSummary(int passed, int total);
    
    // 比较内存内容
    static bool compareMemory(void* addr1, void* addr2, size_t size);
    
    // 验证内存写入
    static bool verifyMemoryWrite(void* addr, const void* expected, size_t size);
};

} // namespace EzorsiaMemory
