#pragma once
#include <unordered_map>
#include <string>
#include <optional>

class I18N {
public:
    enum class Language {
        EN,  // English
        CN,  // Chinese
        KR,  // Korean
        // more languages...
    };

    // Get singleton instance
    static I18N& getInstance();

    // Return std::nullopt if text not found
    std::optional<std::string> getText(unsigned int id) const;
    
    // Set current language and save to config file
    void setLanguage(Language lang);
    
    // Get current language
    Language getCurrentLanguage() const;

    // Convert Language enum to string, for config file read/write
    static std::string languageToString(Language lang);
    static Language stringToLanguage(const std::string& str);

    // Check if text exists for given id
    bool hasText(unsigned int id) const;

private:
    I18N();
    
    I18N(const I18N&) = delete;
    I18N& operator=(const I18N&) = delete;

    Language currentLanguage;
    
    const std::unordered_map<unsigned int, std::string> languageCN;
    const std::unordered_map<unsigned int, std::string> languageEN;
    const std::unordered_map<unsigned int, std::string> languageKR;

    static std::string convertToCurrentCodePage(const std::string& utf8Str);
    static std::unordered_map<unsigned int, std::string> convertMap(
        const std::vector<std::pair<unsigned int, std::string>>& utf8Map);

    // Get current language map
    const std::unordered_map<unsigned int, std::string>* getCurrentMap() const;
};

inline std::string i18n(unsigned int id, const std::string& fallback = "[Missing Text]") {
    auto text = I18N::getInstance().getText(id);
    return text.value_or(fallback);
}