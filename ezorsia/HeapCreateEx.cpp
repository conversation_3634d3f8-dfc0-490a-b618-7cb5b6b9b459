/**
* This code file is sourced from the repository:
 * https://github.com/Willh92/CMS079-ijl15/blob/main/ezorsia/HeapCreateEx.cpp
 *
 * Please refer to the original repository for the most up-to-date version,
 * licensing information, and additional documentation.
 */

#include "stdafx.h"
#include "HeapCreateEx.h"
#include <intrin.h>

#pragma intrinsic(_ReturnAddress)

auto heap1 = HeapCreate(0, 1024, 0);
auto heap2 = HeapCreate(0, 1024, 0);

__declspec(naked) void mapStruct() {
	__asm {
		pushad;
		pushfd;
		mov eax, heap1;
		cmp[eax], 0;
		jne label_jne;
		popfd;
		popad;
		lea eax, [esi + 0x04];
		push eax;
		push 0;
		push 0x004031B0;
		ret;

    label_jne:
		popfd;
		popad;
		lea eax, [esi + 0x04];
		push eax;
		push 0;
		push heap1;
		push 0x004031B7;
		ret;
	}
}

void heapCompact(int ebx) {
	//if (ebx != 0)
	//	std::cout << "½øÈëÉÌ³Ç" << std::endl;
	if (heap1 != NULL)
		HeapCompact(heap1, 0);
	if (heap2 != NULL)
		HeapCompact(heap2, 0);
}

__declspec(naked) void environmentSwitch() {
	__asm {
		pushad;
		pushfd;
		mov esi, [0x00BF14E8];
		mov esi, [esi];
		mov eax, [esi];
		push 0;
		push esi;
		call dword ptr[eax + 0x24]; //freeMap
		push ebx;
		call heapCompact;
		pop ebx;
		popfd;
		popad;
		mov eax, 0x00AB6158;
		push 0x0077734C;
		ret;
	}
}

bool Hook_HeapCreate(bool bEnable)
{
	static auto _HeapCreate = decltype(&HeapCreate)(HeapCreateEx::GetFuncAddress("kernel32.dll", "HeapCreate"));

	decltype(&HeapCreate) Hook = [](DWORD flOptions, SIZE_T dwInitialSize, SIZE_T dwMaximumSize) -> HANDLE
		{
			Hook_HeapCreate(false);
			std::cout << "Hook_HeapCreate Hook ReturnAddress:" << _ReturnAddress() << std::endl;
			HANDLE origin;
			if ((int)_ReturnAddress() == 0x00A6715F) {
				origin = heap2;
			}
			else {
				origin = _HeapCreate(flOptions, dwInitialSize, dwMaximumSize);
				HeapCompact(origin, 0);
			}
			Hook_HeapCreate(true);
			return origin;
		};

	return Memory::SetHook(bEnable, reinterpret_cast<void**>(&_HeapCreate), Hook);
}

void HeapCreateEx::HOOK_HeapCreate()
{
	if (heap1 != NULL && heap2 != NULL)
		std::cout << "Hook_HeapCreate Init state:" << (Hook_HeapCreate(true) == 1 ? "Success" : "Fail") << " heap1:" << heap1 << " heap2:" << heap2 << std::endl;
}

void HeapCreateEx::MemoryOptimization()
{
	Memory::CodeCave(mapStruct, 0x004031AA, 5);
	Memory::CodeCave(environmentSwitch, 0x00777347, 5);
}

DWORD HeapCreateEx::GetFuncAddress(LPCSTR lpModule, LPCSTR lpFunc)
{
	HMODULE mod = LoadLibraryA(lpModule); // ty alias! for sharing their version	//ps i made some changes
	if (!mod)
	{
		std::cout << "GetFuncAddress failed at " << lpModule << std::endl;
		return 0;
	}
	DWORD address = (DWORD)GetProcAddress(mod, lpFunc);
	if (!address)
	{
		std::cout << "GetFuncAddress failed at " << lpFunc << std::endl;
		return 0;
	}
	return address;
}