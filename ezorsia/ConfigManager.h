#pragma once

#include <string>
#include <memory>
#include <optional>
#include <vector>
#include "INIReader.h"

namespace EzorsiaConfig {

// ===== 配置结构定义 =====

/// 游戏显示配置
struct GameDisplayConfig {
    int width = 1280;
    int height = 720;
    bool windowedMode = true;
    bool removeLogos = true;
    int msgAmount = 26;
    
    bool isValid() const {
        return width > 0 && height > 0 && 
               width <= 3840 && height <= 2160 &&  // 4K限制
               msgAmount >= 6 && msgAmount <= 255;
    }
};

/// 网络配置
struct NetworkConfig {
    std::string serverIP = "127.0.0.1";
    int serverPort = 8484;
    std::string language = "CN";
    
    bool isValid() const {
        return !serverIP.empty() && 
               serverPort > 0 && serverPort <= 65535 &&
               (language == "CN" || language == "EN" || language == "KR");
    }
};

/// 游戏机制配置
struct GameMechanicsConfig {
    double damageCap = 199999.0;
    bool useTubi = false;
    int speedMovementCap = 140;
    uint32_t jumpCap = 123;
    bool climbSpeedAuto = false;
    float climbSpeed = 1.0f;
    
    bool isValid() const {
        return damageCap > 0 && damageCap <= 999999999.0 &&
               speedMovementCap > 0 && speedMovementCap <= 1000 &&
               jumpCap > 0 && jumpCap <= 1000 &&
               climbSpeed > 0.0f && climbSpeed <= 10.0f;
    }
};

/// 可选功能配置
struct OptionalFeaturesConfig {
    bool extraQuickslots = false;
    bool memoryOptimize = false;
    int memoryOptimizeDelay = 0;
    bool inOutLinkSupport = true;
    bool experimentalFeature = false;
    
    // MainMain相关配置
    bool ownCashShopFrame = false;
    bool useV62_ExpTable = false;
    bool customLoginFrame = false;
    
    // 自定义DLL配置
    std::string customDll1 = "CUSTOM.dll";
    std::string customDll2 = "CUSTOM2.dll";
    std::string customDll3 = "CUSTOM3.dll";
    
    bool isValid() const {
        return memoryOptimizeDelay >= 0 && memoryOptimizeDelay <= 10000;
    }
};

/// 调试配置
struct DebugConfig {
    bool showConsole = false;
    int sleepTime = 0;
    
    bool isValid() const {
        return sleepTime >= 0 && sleepTime <= 10000;
    }
};

/// 完整配置结构
struct EzorsiaConfiguration {
    GameDisplayConfig display;
    NetworkConfig network;
    GameMechanicsConfig mechanics;
    OptionalFeaturesConfig optional;
    DebugConfig debug;
    
    bool isValid() const {
        return display.isValid() && 
               network.isValid() && 
               mechanics.isValid() && 
               optional.isValid() && 
               debug.isValid();
    }
};

// ===== 配置管理器接口 =====

/// 配置验证结果
struct ValidationResult {
    bool isValid = true;
    std::vector<std::string> errors;
    
    void addError(const std::string& error) {
        isValid = false;
        errors.push_back(error);
    }
};

/// 配置管理器接口
class IConfigManager {
public:
    virtual ~IConfigManager() = default;
    
    /// 加载配置
    virtual bool loadConfig(const std::string& configPath = "config.ini") = 0;
    
    /// 保存配置
    virtual bool saveConfig(const std::string& configPath = "config.ini") const = 0;
    
    /// 获取完整配置
    virtual const EzorsiaConfiguration& getConfig() const = 0;
    
    /// 验证配置
    virtual ValidationResult validateConfig() const = 0;
    
    /// 重置为默认配置
    virtual void resetToDefaults() = 0;
    
    /// 检查配置文件是否存在
    virtual bool configFileExists(const std::string& configPath = "config.ini") const = 0;
};

// ===== 具体实现 =====

/// INI文件配置管理器实现
class INIConfigManager : public IConfigManager {
private:
    EzorsiaConfiguration config_;
    std::unique_ptr<INIReader> reader_;
    mutable std::string lastError_;
    
    // 内部辅助方法
    void loadDisplayConfig();
    void loadNetworkConfig();
    void loadMechanicsConfig();
    void loadOptionalConfig();
    void loadDebugConfig();
    
    bool createDefaultConfigFile(const std::string& configPath) const;
    
public:
    INIConfigManager() = default;
    ~INIConfigManager() = default;
    
    // 禁用拷贝构造和赋值
    INIConfigManager(const INIConfigManager&) = delete;
    INIConfigManager& operator=(const INIConfigManager&) = delete;
    
    // IConfigManager接口实现
    bool loadConfig(const std::string& configPath = "config.ini") override;
    bool saveConfig(const std::string& configPath = "config.ini") const override;
    const EzorsiaConfiguration& getConfig() const override { return config_; }
    ValidationResult validateConfig() const override;
    void resetToDefaults() override;
    bool configFileExists(const std::string& configPath = "config.ini") const override;
    
    // 获取最后的错误信息
    const std::string& getLastError() const { return lastError_; }
};

// ===== 全局配置管理器 =====

/// 获取全局配置管理器实例
INIConfigManager& getConfigManager();

/// 便捷访问函数
const EzorsiaConfiguration& getConfig();
const GameDisplayConfig& getDisplayConfig();
const NetworkConfig& getNetworkConfig();
const GameMechanicsConfig& getMechanicsConfig();
const OptionalFeaturesConfig& getOptionalConfig();
const DebugConfig& getDebugConfig();

} // namespace EzorsiaConfig
