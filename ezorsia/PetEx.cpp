#pragma once
#include "stdafx.h"
#include "PetEx.h"
#include "Resman.h"
#include <iomanip>
#include <sstream>
#include <string>
#include "Memory.h"


int __stdcall _checkCanEquip(int equipId, int petId) {
	// std::cout << "equipId = " << equipId << " petId = " << petId;
	std::wostringstream oss;
	if (equipId / 10000 == 180 && petId / 10000 == 500) {
		oss << L"Character/PetEquip/";
		oss << std::setw(8) << std::setfill(L'0') << equipId;
		oss << L".img/";
		oss << petId;
		std::wstring path = oss.str();
		auto pro = getIWzPropertyForPath(path);
		// std::wcout << path << L" " << pro << std::endl;
		return pro != nullptr;
	}
	return 0;
}

const DWORD cPetOriginalPopEcxAddress = 0x0046D41B;
const DWORD cPetreturnFalseAddress = 0x0046D478;
const DWORD cPetreturnTrueddress = 0x0046D47A;
__declspec(naked) void checkCanEquip()
{
    __asm {
        pop ecx
        pop ecx
        push[ebp + 0x8]    // petId
        push eax           // equipId
        call _checkCanEquip

        cmp eax, 1h
        jnz short return_false
        mov eax, 1
        jmp short return_to_original

        return_false:
            jmp cPetreturnFalseAddress
        return_to_original:
            jmp cPetreturnTrueddress
    }
}

void PetEx::HookPetCheckCanEquip(bool enable) {
    if (!enable)
        return;
    Memory::CodeCave(checkCanEquip, cPetOriginalPopEcxAddress, cPetreturnFalseAddress - cPetOriginalPopEcxAddress);
}


BOOL __fastcall _IsItemSuitedForPet_Hook(void *pThis, void *edx, int a2) {
	// std::cout << "IsItemSuitedForPet is hooked" << std::endl;
	if (a2 >= 5000000 && a2 <= 5000999) {
		return 1;
	}
	return 0;
}

void PetEx::HookUnlimtPetEquipWear(bool enable) {
    if (!enable)
        return;

	auto _IsItemSuitedForPet = reinterpret_cast<IsItemSuitedForPet_t>(0x0046D408);
	Memory::SetHook(enable, reinterpret_cast<void **>(&_IsItemSuitedForPet), _IsItemSuitedForPet_Hook);
}