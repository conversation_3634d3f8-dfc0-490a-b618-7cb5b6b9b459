#include "stdafx.h"
#include "MemoryRefactorTest.h"
#include "Memory.h"
#include "MemoryAdapter.h"
#include <iostream>
#include <cstring>

namespace EzorsiaMemory {

bool MemoryRefactorTest::runAllTests() {
    std::cout << "\n=== Memory Refactor Test Suite ===" << std::endl;
    
    int passed = 0;
    int total = 0;
    
    // 兼容性测试
    total++; if (testMemoryCompatibility()) passed++;
    total++; if (testMemoryAdapterConsistency()) passed++;
    total++; if (testUseVirtuProtectSync()) passed++;
    total++; if (testErrorHandlingFallback()) passed++;
    
    printTestSummary(passed, total);
    return passed == total;
}

bool MemoryRefactorTest::testMemoryCompatibility() {
    std::cout << "Testing Memory class compatibility..." << std::endl;
    
    try {
        // 分配测试内存
        void* testMem = nullptr;
        if (!allocateTestMemory(&testMem, 1024)) {
            logTestResult("Memory Compatibility", false);
            return false;
        }
        
        DWORD testAddr = reinterpret_cast<DWORD>(testMem);
        
        // 测试WriteByte
        Memory::WriteByte(testAddr, 0xAB);
        if (*reinterpret_cast<uint8_t*>(testAddr) != 0xAB) {
            std::cout << "  WriteByte test failed" << std::endl;
            freeTestMemory(testMem);
            logTestResult("Memory Compatibility", false);
            return false;
        }
        
        // 测试WriteInt
        Memory::WriteInt(testAddr + 4, 0x12345678);
        if (*reinterpret_cast<uint32_t*>(testAddr + 4) != 0x12345678) {
            std::cout << "  WriteInt test failed" << std::endl;
            freeTestMemory(testMem);
            logTestResult("Memory Compatibility", false);
            return false;
        }
        
        // 测试WriteString
        const char* testStr = "Hello";
        Memory::WriteString(testAddr + 8, testStr);
        if (memcmp(reinterpret_cast<void*>(testAddr + 8), testStr, strlen(testStr)) != 0) {
            std::cout << "  WriteString test failed" << std::endl;
            freeTestMemory(testMem);
            logTestResult("Memory Compatibility", false);
            return false;
        }
        
        // 测试FillBytes
        Memory::FillBytes(testAddr + 16, 0xCC, 8);
        bool fillSuccess = true;
        for (int i = 0; i < 8; i++) {
            if (*reinterpret_cast<uint8_t*>(testAddr + 16 + i) != 0xCC) {
                fillSuccess = false;
                break;
            }
        }
        
        if (!fillSuccess) {
            std::cout << "  FillBytes test failed" << std::endl;
            freeTestMemory(testMem);
            logTestResult("Memory Compatibility", false);
            return false;
        }
        
        freeTestMemory(testMem);
        logTestResult("Memory Compatibility", true);
        return true;
    }
    catch (const std::exception& e) {
        std::cout << "  Exception: " << e.what() << std::endl;
        logTestResult("Memory Compatibility", false);
        return false;
    }
}

bool MemoryRefactorTest::testMemoryAdapterConsistency() {
    std::cout << "Testing Memory-MemoryAdapter consistency..." << std::endl;
    
    try {
        // 分配两块测试内存
        void* testMem1 = nullptr;
        void* testMem2 = nullptr;
        if (!allocateTestMemory(&testMem1, 1024) || !allocateTestMemory(&testMem2, 1024)) {
            if (testMem1) freeTestMemory(testMem1);
            if (testMem2) freeTestMemory(testMem2);
            logTestResult("Memory-MemoryAdapter Consistency", false);
            return false;
        }
        
        DWORD addr1 = reinterpret_cast<DWORD>(testMem1);
        DWORD addr2 = reinterpret_cast<DWORD>(testMem2);
        
        // 使用Memory和MemoryAdapter执行相同操作
        Memory::WriteByte(addr1, 0xAB);
        MemoryAdapter::WriteByte(addr2, 0xAB);
        
        Memory::WriteInt(addr1 + 4, 0x12345678);
        MemoryAdapter::WriteInt(addr2 + 4, 0x12345678);
        
        const char* testStr = "Test";
        Memory::WriteString(addr1 + 8, testStr);
        MemoryAdapter::WriteString(addr2 + 8, testStr);
        
        Memory::FillBytes(addr1 + 16, 0xCC, 8);
        MemoryAdapter::FillBytes(addr2 + 16, 0xCC, 8);
        
        // 比较结果
        bool consistent = compareMemory(testMem1, testMem2, 32);
        
        freeTestMemory(testMem1);
        freeTestMemory(testMem2);
        
        if (!consistent) {
            std::cout << "  Memory operations are not consistent" << std::endl;
        }
        
        logTestResult("Memory-MemoryAdapter Consistency", consistent);
        return consistent;
    }
    catch (const std::exception& e) {
        std::cout << "  Exception: " << e.what() << std::endl;
        logTestResult("Memory-MemoryAdapter Consistency", false);
        return false;
    }
}

bool MemoryRefactorTest::testUseVirtuProtectSync() {
    std::cout << "Testing UseVirtuProtect synchronization..." << std::endl;
    
    try {
        // 测试设置同步
        bool originalValue = Memory::UseVirtuProtect;
        
        // 改变Memory的设置
        Memory::UseVirtuProtect = !originalValue;
        
        // 分配测试内存
        void* testMem = nullptr;
        if (!allocateTestMemory(&testMem, 64)) {
            Memory::UseVirtuProtect = originalValue;
            logTestResult("UseVirtuProtect Sync", false);
            return false;
        }
        
        DWORD testAddr = reinterpret_cast<DWORD>(testMem);
        
        // 执行操作，应该会同步设置
        Memory::WriteByte(testAddr, 0xAB);
        
        // 验证写入成功
        bool success = (*reinterpret_cast<uint8_t*>(testAddr) == 0xAB);
        
        freeTestMemory(testMem);
        Memory::UseVirtuProtect = originalValue;
        
        logTestResult("UseVirtuProtect Sync", success);
        return success;
    }
    catch (const std::exception& e) {
        std::cout << "  Exception: " << e.what() << std::endl;
        logTestResult("UseVirtuProtect Sync", false);
        return false;
    }
}

bool MemoryRefactorTest::testErrorHandlingFallback() {
    std::cout << "Testing error handling fallback..." << std::endl;
    
    try {
        // 这个测试比较复杂，因为我们需要模拟新系统失败的情况
        // 目前我们只测试基本的错误处理路径存在
        
        void* testMem = nullptr;
        if (!allocateTestMemory(&testMem, 64)) {
            logTestResult("Error Handling Fallback", false);
            return false;
        }
        
        DWORD testAddr = reinterpret_cast<DWORD>(testMem);
        
        // 执行一些操作，确保不会崩溃
        Memory::WriteByte(testAddr, 0xAB);
        Memory::WriteInt(testAddr + 4, 0x12345678);
        
        bool success = (*reinterpret_cast<uint8_t*>(testAddr) == 0xAB) &&
                      (*reinterpret_cast<uint32_t*>(testAddr + 4) == 0x12345678);
        
        freeTestMemory(testMem);
        
        logTestResult("Error Handling Fallback", success);
        return success;
    }
    catch (const std::exception& e) {
        std::cout << "  Exception: " << e.what() << std::endl;
        logTestResult("Error Handling Fallback", false);
        return false;
    }
}

// 辅助方法实现
bool MemoryRefactorTest::allocateTestMemory(void** memory, size_t size) {
    *memory = VirtualAlloc(nullptr, size, MEM_COMMIT | MEM_RESERVE, PAGE_EXECUTE_READWRITE);
    return *memory != nullptr;
}

void MemoryRefactorTest::freeTestMemory(void* memory) {
    if (memory) {
        VirtualFree(memory, 0, MEM_RELEASE);
    }
}

void MemoryRefactorTest::logTestResult(const std::string& testName, bool passed) {
    std::cout << "  " << testName << ": " << (passed ? "PASSED" : "FAILED") << std::endl;
}

void MemoryRefactorTest::printTestSummary(int passed, int total) {
    std::cout << "\n=== Memory Refactor Test Summary ===" << std::endl;
    std::cout << "Passed: " << passed << "/" << total << std::endl;
    std::cout << "Success Rate: " << (total > 0 ? (passed * 100 / total) : 0) << "%" << std::endl;
    
    if (passed == total) {
        std::cout << "All tests PASSED! Memory refactoring is successful." << std::endl;
    } else {
        std::cout << "Some tests FAILED. Please review the refactoring." << std::endl;
    }
}

bool MemoryRefactorTest::compareMemory(void* addr1, void* addr2, size_t size) {
    return memcmp(addr1, addr2, size) == 0;
}

bool MemoryRefactorTest::verifyMemoryWrite(void* addr, const void* expected, size_t size) {
    return memcmp(addr, expected, size) == 0;
}

} // namespace EzorsiaMemory
