#include "stdafx.h"
#include "MemoryManagerTest.h"
#include <iostream>
#include <chrono>

namespace EzorsiaMemory {

bool MemoryManagerTest::runAllTests() {
    std::cout << "\n=== Memory Manager Test Suite ===" << std::endl;
    
    int passed = 0;
    int total = 0;
    
    // 基础功能测试
    total++; if (testBasicMemoryOperations()) passed++;
    total++; if (testAddressValidation()) passed++;
    total++; if (testAddressRegistry()) passed++;
    total++; if (testMemoryAdapter()) passed++;
    
    // 错误处理测试
    total++; if (testErrorHandling()) passed++;
    total++; if (testInvalidAddresses()) passed++;
    
    // 兼容性测试
    total++; if (testBackwardCompatibility()) passed++;
    
    printTestSummary(passed, total);
    return passed == total;
}

bool MemoryManagerTest::testBasicMemoryOperations() {
    std::cout << "\n--- Testing Basic Memory Operations ---" << std::endl;
    
    try {
        auto& patcher = getMemoryPatcher();
        
        // 分配测试内存
        void* testMem = nullptr;
        if (!allocateTestMemory(&testMem, 1024)) {
            logTestResult("Basic Memory Operations", false);
            return false;
        }
        
        Address testAddr = reinterpret_cast<Address>(testMem);
        
        // 测试字节写入
        auto result1 = patcher.writeByte(testAddr, 0xAB);
        if (!result1) {
            std::cout << "  WriteByte failed: " << result1.getError() << std::endl;
            freeTestMemory(testMem);
            logTestResult("Basic Memory Operations", false);
            return false;
        }
        
        // 验证写入
        if (*reinterpret_cast<uint8_t*>(testAddr) != 0xAB) {
            std::cout << "  WriteByte verification failed" << std::endl;
            freeTestMemory(testMem);
            logTestResult("Basic Memory Operations", false);
            return false;
        }
        
        // 测试整数写入
        auto result2 = patcher.writeInt(testAddr + 4, 0x12345678);
        if (!result2) {
            std::cout << "  WriteInt failed: " << result2.getError() << std::endl;
            freeTestMemory(testMem);
            logTestResult("Basic Memory Operations", false);
            return false;
        }
        
        // 验证写入
        if (*reinterpret_cast<uint32_t*>(testAddr + 4) != 0x12345678) {
            std::cout << "  WriteInt verification failed" << std::endl;
            freeTestMemory(testMem);
            logTestResult("Basic Memory Operations", false);
            return false;
        }
        
        // 测试字符串写入
        std::string testStr = "Hello";
        auto result3 = patcher.writeString(testAddr + 8, testStr);
        if (!result3) {
            std::cout << "  WriteString failed: " << result3.getError() << std::endl;
            freeTestMemory(testMem);
            logTestResult("Basic Memory Operations", false);
            return false;
        }
        
        // 验证写入
        if (std::memcmp(reinterpret_cast<void*>(testAddr + 8), testStr.c_str(), testStr.length()) != 0) {
            std::cout << "  WriteString verification failed" << std::endl;
            freeTestMemory(testMem);
            logTestResult("Basic Memory Operations", false);
            return false;
        }
        
        // 测试字节填充
        auto result4 = patcher.fillBytes(testAddr + 16, 0xFF, 32);
        if (!result4) {
            std::cout << "  FillBytes failed: " << result4.getError() << std::endl;
            freeTestMemory(testMem);
            logTestResult("Basic Memory Operations", false);
            return false;
        }
        
        // 验证填充
        for (int i = 0; i < 32; i++) {
            if (*reinterpret_cast<uint8_t*>(testAddr + 16 + i) != 0xFF) {
                std::cout << "  FillBytes verification failed at offset " << i << std::endl;
                freeTestMemory(testMem);
                logTestResult("Basic Memory Operations", false);
                return false;
            }
        }
        
        freeTestMemory(testMem);
        logTestResult("Basic Memory Operations", true);
        return true;
    }
    catch (const std::exception& e) {
        std::cout << "  Exception: " << e.what() << std::endl;
        logTestResult("Basic Memory Operations", false);
        return false;
    }
}

bool MemoryManagerTest::testAddressValidation() {
    std::cout << "\n--- Testing Address Validation ---" << std::endl;
    
    try {
        auto& patcher = getMemoryPatcher();
        
        // 测试无效地址（空指针）
        auto result1 = patcher.writeByte(0, 0x00);
        if (result1) {
            std::cout << "  Null address validation failed - should have been rejected" << std::endl;
            logTestResult("Address Validation", false);
            return false;
        }
        
        // 测试无效地址（超出范围）
        auto result2 = patcher.writeByte(0x7FFFFFFF, 0x00);
        if (result2) {
            std::cout << "  Out-of-range address validation failed - should have been rejected" << std::endl;
            logTestResult("Address Validation", false);
            return false;
        }
        
        std::cout << "  Address validation working correctly" << std::endl;
        logTestResult("Address Validation", true);
        return true;
    }
    catch (const std::exception& e) {
        std::cout << "  Exception: " << e.what() << std::endl;
        logTestResult("Address Validation", false);
        return false;
    }
}

bool MemoryManagerTest::testAddressRegistry() {
    std::cout << "\n--- Testing Address Registry ---" << std::endl;
    
    try {
        auto& manager = getAddressManager();
        
        // 测试地址注册
        auto result1 = manager.registerAddress("TEST_ADDR", 0x00400000, 4);
        if (!result1) {
            std::cout << "  Address registration failed: " << result1.getError() << std::endl;
            logTestResult("Address Registry", false);
            return false;
        }
        
        // 测试地址获取
        auto result2 = manager.getAddress("TEST_ADDR");
        if (!result2) {
            std::cout << "  Address retrieval failed: " << result2.getError() << std::endl;
            logTestResult("Address Registry", false);
            return false;
        }
        
        if (*result2 != 0x00400000) {
            std::cout << "  Address retrieval returned wrong value: 0x" << std::hex << *result2 << std::dec << std::endl;
            logTestResult("Address Registry", false);
            return false;
        }
        
        // 测试不存在的地址
        auto result3 = manager.getAddress("NONEXISTENT_ADDR");
        if (result3) {
            std::cout << "  Non-existent address should have failed" << std::endl;
            logTestResult("Address Registry", false);
            return false;
        }
        
        std::cout << "  Address registry working correctly" << std::endl;
        logTestResult("Address Registry", true);
        return true;
    }
    catch (const std::exception& e) {
        std::cout << "  Exception: " << e.what() << std::endl;
        logTestResult("Address Registry", false);
        return false;
    }
}

bool MemoryManagerTest::testMemoryAdapter() {
    std::cout << "\n--- Testing Memory Adapter ---" << std::endl;
    
    try {
        // 分配测试内存
        void* testMem = nullptr;
        if (!allocateTestMemory(&testMem, 256)) {
            logTestResult("Memory Adapter", false);
            return false;
        }
        
        DWORD testAddr = reinterpret_cast<DWORD>(testMem);
        
        // 测试适配器的安全方法
        auto result1 = MemoryAdapter::SafeWriteByte(testAddr, 0xCD);
        if (!result1) {
            std::cout << "  SafeWriteByte failed: " << result1.getError() << std::endl;
            freeTestMemory(testMem);
            logTestResult("Memory Adapter", false);
            return false;
        }
        
        // 验证写入
        if (*reinterpret_cast<uint8_t*>(testAddr) != 0xCD) {
            std::cout << "  SafeWriteByte verification failed" << std::endl;
            freeTestMemory(testMem);
            logTestResult("Memory Adapter", false);
            return false;
        }
        
        // 测试兼容性方法（不抛出异常）
        MemoryAdapter::WriteByte(testAddr + 1, 0xEF);
        if (*reinterpret_cast<uint8_t*>(testAddr + 1) != 0xEF) {
            std::cout << "  Compatibility WriteByte verification failed" << std::endl;
            freeTestMemory(testMem);
            logTestResult("Memory Adapter", false);
            return false;
        }
        
        freeTestMemory(testMem);
        std::cout << "  Memory adapter working correctly" << std::endl;
        logTestResult("Memory Adapter", true);
        return true;
    }
    catch (const std::exception& e) {
        std::cout << "  Exception: " << e.what() << std::endl;
        logTestResult("Memory Adapter", false);
        return false;
    }
}

bool MemoryManagerTest::testErrorHandling() {
    std::cout << "\n--- Testing Error Handling ---" << std::endl;
    
    try {
        // 测试各种错误情况
        auto result1 = MemoryAdapter::SafeWriteString(0, nullptr);
        if (result1) {
            std::cout << "  Null string should have failed" << std::endl;
            logTestResult("Error Handling", false);
            return false;
        }
        
        auto result2 = MemoryAdapter::SafeWriteByteArray(0x00400000, nullptr, 10);
        if (result2) {
            std::cout << "  Null byte array should have failed" << std::endl;
            logTestResult("Error Handling", false);
            return false;
        }
        
        std::cout << "  Error handling working correctly" << std::endl;
        logTestResult("Error Handling", true);
        return true;
    }
    catch (const std::exception& e) {
        std::cout << "  Exception: " << e.what() << std::endl;
        logTestResult("Error Handling", false);
        return false;
    }
}

bool MemoryManagerTest::testInvalidAddresses() {
    std::cout << "\n--- Testing Invalid Address Handling ---" << std::endl;
    
    // 这个测试主要验证地址验证器是否正确拒绝无效地址
    auto result1 = MemoryAdapter::SafeWriteByte(0, 0x00);
    auto result2 = MemoryAdapter::SafeWriteByte(0xFFFFFFFF, 0x00);
    
    if (result1 || result2) {
        std::cout << "  Invalid addresses should have been rejected" << std::endl;
        logTestResult("Invalid Address Handling", false);
        return false;
    }
    
    std::cout << "  Invalid address handling working correctly" << std::endl;
    logTestResult("Invalid Address Handling", true);
    return true;
}

bool MemoryManagerTest::testBackwardCompatibility() {
    std::cout << "\n--- Testing Backward Compatibility ---" << std::endl;
    
    try {
        // 分配测试内存
        void* testMem = nullptr;
        if (!allocateTestMemory(&testMem, 128)) {
            logTestResult("Backward Compatibility", false);
            return false;
        }
        
        DWORD testAddr = reinterpret_cast<DWORD>(testMem);
        
        // 测试宏定义的兼容性
        Memory_WriteByte(testAddr, 0x42);
        if (*reinterpret_cast<uint8_t*>(testAddr) != 0x42) {
            std::cout << "  Macro compatibility failed" << std::endl;
            freeTestMemory(testMem);
            logTestResult("Backward Compatibility", false);
            return false;
        }
        
        Memory_WriteInt(testAddr + 4, 0x87654321);
        if (*reinterpret_cast<uint32_t*>(testAddr + 4) != 0x87654321) {
            std::cout << "  Macro WriteInt compatibility failed" << std::endl;
            freeTestMemory(testMem);
            logTestResult("Backward Compatibility", false);
            return false;
        }
        
        freeTestMemory(testMem);
        std::cout << "  Backward compatibility working correctly" << std::endl;
        logTestResult("Backward Compatibility", true);
        return true;
    }
    catch (const std::exception& e) {
        std::cout << "  Exception: " << e.what() << std::endl;
        logTestResult("Backward Compatibility", false);
        return false;
    }
}

// 辅助方法实现
void MemoryManagerTest::logTestResult(const std::string& testName, bool passed) {
    std::cout << "  [" << (passed ? "PASS" : "FAIL") << "] " << testName << std::endl;
}

bool MemoryManagerTest::allocateTestMemory(void** ptr, size_t size) {
    *ptr = VirtualAlloc(nullptr, size, MEM_COMMIT | MEM_RESERVE, PAGE_READWRITE);
    if (!*ptr) {
        std::cout << "  Failed to allocate test memory" << std::endl;
        return false;
    }
    return true;
}

void MemoryManagerTest::freeTestMemory(void* ptr) {
    if (ptr) {
        VirtualFree(ptr, 0, MEM_RELEASE);
    }
}

void MemoryManagerTest::printTestSummary(int passed, int total) {
    std::cout << "\n=== Test Summary ===" << std::endl;
    std::cout << "Passed: " << passed << "/" << total << std::endl;
    std::cout << "Success Rate: " << (total > 0 ? (passed * 100 / total) : 0) << "%" << std::endl;
    
    if (passed == total) {
        std::cout << "🎉 All tests passed!" << std::endl;
    } else {
        std::cout << "❌ Some tests failed. Please review the output above." << std::endl;
    }
}

} // namespace EzorsiaMemory
