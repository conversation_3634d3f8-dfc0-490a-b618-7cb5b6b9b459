before submitting features requests or modifications please remember that the patch is primarily used to improve play experience in a client with resolution scaled to the aspect ratio of modern monitors while keeping a gameplay experience similar to the original game

advice on code and project structure is always appreciated and accepted. i realize that the code i'm writing may not be industry standard or good practice, and may not be well-designed to be easily changed and maintained in the future. so help with this would be appreciated

all edits are accepted as contributions, but non-resolution related edits must have the option to turn it off in the config file. additionally non-resolution related edits will be off by default in the config file for public releases
	- also depending on the circumstances, some resolution related edits may also require the option to be toggled off

if you have an address without any specific application but of which you know the functionality of, those will also be accepted and put in as comments to be kept for reference in case it ever becomes needed


if you want to make a contribution you can submit a pull request or contact me on discord. i greatly appreciate the assistance =)
